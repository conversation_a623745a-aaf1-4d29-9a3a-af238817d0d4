{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 最適化手法比較サマリー ===\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import numpy as np\n", "import os\n", "\n", "# 結果ファイルのパス\n", "mip_results_path = 'result/MIP_aggregate_results.csv'\n", "multistart_results_path = 'result/multi_start_aggregate_results.csv'\n", "\n", "print(\"=== 最適化手法比較サマリー ===\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MIP結果ファイルの読み込みエラー: 'utf-8' codec can't decode byte 0x83 in position 0: invalid start byte\n", "MIP結果ファイルを読み込みました（Shift_JIS）\n", "  ファイル名    目的関数値      計算時間\n", "0   D36  52230.0  4.194412\n", "1    合計  52230.0  4.194412\n"]}], "source": ["# CSVファイルを読み込み\n", "try:\n", "    mip_df = pd.read_csv(mip_results_path, encoding='utf-8')\n", "    print(\"MIP結果ファイルを読み込みました\")\n", "    print(mip_df)\n", "except Exception as e:\n", "    print(f\"MIP結果ファイルの読み込みエラー: {e}\")\n", "    # エンコーディングを変更して再試行\n", "    try:\n", "        mip_df = pd.read_csv(mip_results_path, encoding='shift_jis')\n", "        print(\"MIP結果ファイルを読み込みました（Shift_JIS）\")\n", "        print(mip_df)\n", "    except Exception as e2:\n", "        print(f\"MIP結果ファイルの読み込みに失敗: {e2}\")\n", "        mip_df = None"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "多スタートローカルサーチ結果ファイルを読み込みました\n", "  ファイル名         目的関数値      計算時間\n", "0   D36  72295.909091  0.021127\n", "1    合計  72295.909091  0.021127\n"]}], "source": ["try:\n", "    multistart_df = pd.read_csv(multistart_results_path, encoding='utf-8')\n", "    print(\"\\n多スタートローカルサーチ結果ファイルを読み込みました\")\n", "    print(multistart_df)\n", "except Exception as e:\n", "    print(f\"多スタートローカルサーチ結果ファイルの読み込みエラー: {e}\")\n", "    multistart_df = None"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 合計値比較 ===\n", "MIP - 目的関数値: 52230.00, 計算時間: 4.1944秒\n", "多スタートローカルサーチ - 目的関数値: 72295.91, 計算時間: 0.0211秒\n", "\n", "=== 比較結果 ===\n", "目的関数値の差: 20065.91\n", "目的関数値の変化率: 38.42% (正の値は多スタートが悪い)\n", "計算時間の比率: 0.01 (多スタート/MIP)\n"]}], "source": ["# データの前処理と比較\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行を抽出\n", "    mip_total = mip_df[mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_total = multistart_df[multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    if len(mip_total) > 0 and len(multistart_total) > 0:\n", "        # 合計値を取得\n", "        mip_objective = float(mip_total.iloc[0, 1])  # 目的関数値\n", "        mip_time = float(mip_total.iloc[0, 2])       # 計算時間\n", "        \n", "        multistart_objective = float(multistart_total.iloc[0, 1])  # 目的関数値\n", "        multistart_time = float(multistart_total.iloc[0, 2])       # 計算時間\n", "        \n", "        print(f\"\\n=== 合計値比較 ===\")\n", "        print(f\"MIP - 目的関数値: {mip_objective:.2f}, 計算時間: {mip_time:.4f}秒\")\n", "        print(f\"多スタートローカルサーチ - 目的関数値: {multistart_objective:.2f}, 計算時間: {multistart_time:.4f}秒\")\n", "        \n", "        # 改善率の計算\n", "        objective_improvement = ((multistart_objective - mip_objective) / mip_objective) * 100\n", "        time_ratio = multistart_time / mip_time\n", "        \n", "        print(f\"\\n=== 比較結果 ===\")\n", "        print(f\"目的関数値の差: {multistart_objective - mip_objective:.2f}\")\n", "        print(f\"目的関数値の変化率: {objective_improvement:.2f}% (正の値は多スタートが悪い)\")\n", "        print(f\"計算時間の比率: {time_ratio:.2f} (多スタート/MIP)\")\n", "    else:\n", "        print(\"合計行が見つかりませんでした\")\n", "        mip_objective = mip_time = multistart_objective = multistart_time = None\n", "else:\n", "    print(\"データの読み込みに失敗したため、比較できません\")\n", "    mip_objective = mip_time = multistart_objective = multistart_time = None"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 個別データセット比較 ===\n", "MIP個別結果:\n", "  ファイル名    目的関数値      計算時間\n", "0   D36  52230.0  4.194412\n", "\n", "多スタートローカルサーチ個別結果:\n", "  ファイル名         目的関数値      計算時間\n", "0   D36  72295.909091  0.021127\n"]}], "source": ["# 個別データセットの比較も行う\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行以外のデータを取得\n", "    mip_individual = mip_df[~mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_individual = multistart_df[~multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    print(f\"\\n=== 個別データセット比較 ===\")\n", "    print(f\"MIP個別結果:\")\n", "    print(mip_individual)\n", "    print(f\"\\n多スタートローカルサーチ個別結果:\")\n", "    print(multistart_individual)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "比較プロットを保存しました: result/optimization_comparison.png\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAJOCAYAAABYwk4SAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACgf0lEQVR4nOzdeVxU9ffH8fewuksimDDghuWeSbaYtrgrYpmZS5laWW6/SjTFzN3Cytwy08o0zSzNyn0LrTS3RFuVTE1xgASXWFRAmPn9wZfJcWYUXJixXs/H4yb33nPv59zpMnPn8Lmfa7BYLBYBAAAAAAAAAAA7Hq5OAAAAAAAAAAAAd0URHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAwOWOHj2qX375xen6L7/8UseOHSvGjK4Pk8mksWPHXjJm/vz5WrVqVfEkBAAAALdy8OBBZWZmujoNt/Pxxx/rm2++cbr+yJEjGjt2LK8dgOuGIjoAuLF169bpt99+s1tuNpv1+++/X3b78+fP68SJEw6n06dPS5LOnDnjNCYtLc1mfxaLRfv27ZOUfyFbsWJFSdJvv/2mPXv2WOOeffZZjRo1qtDHOWvWLHXq1MnhuoyMDD3yyCOKjY0t9P6ulSlTpujjjz92uO7//u//tGTJkiLtz2Qyady4cZeMoYgOAABw5ZKSkhQfH283nTt3zi62atWqGj9+vNN95eXl6cUXX9QPP/zgcP2KFSs0cuTIa5a7JNWsWfOy14JpaWn666+/HE4Fx+ls/V9//SWz2XxNc77Yvn379OKLL+rEiRN267799lt169atyDkUpog+btw4iugArhuK6ABwDb344osyGAxFmpzJy8tTly5d9Mknn9it++GHH1SrVi1t2LDhkvl8//33CggIcDjVr19fkjR8+HCnMQ899JDN/pYsWaIGDRpo//79NstfeeUVDRkyxDq/ZcuWIl3Abt68Wffdd5/DdSaTSVL+F4ri9sknn2jdunUO161cuVI///xzMWcEAACAS3n++edVu3ZtuykuLs4uNjc395LF3NmzZ2v69Ok6ceKEjhw5YjOdPXtWw4cP1/fff2+37siRI0pJSbHux2Qyad26dXaTo8J+YbzwwguqXLmyw+mzzz5Tbm6u0/WVK1dWUlLSFbVbWIcPH9b06dP1999/2637888/9dlnn133Qj4AXGterk4AAP5tgoKCCtVresGCBYqJiXG6fu/evcrMzNQjjzxit279+vXy9fVV06ZNC5XTsWPHZDQarfOffvqphg4dap3v3LmzPv/8c5ttevfurSNHjtgse/TRRzVixAhNnz7d2vaBAwe0cuVKbdy4UVJ+7/dDhw6pTp06hcotNTVVe/bs0f/93/9p7NixTntqX3ysVapUscvvQhaLRStXrtSqVasUFxen1NRUGQwGhYSE6L777tPTTz+tGjVqFCrHq/Hpp5+qe/fuNssu/OPJ8uXLVb16dev8mTNndPr0af3666/WZT4+Prrllluue64AAAA3uouvaa/U/v379corr0iS2rdvb7e+U6dO1l7u1apVs1sfERFh7VG+bt069e3bV76+vpLyr1NzcnL0559/ysvLy67zSUFv+gI33XSTKlWqZBPTokULff311zbLLrzel/I7qjzwwAPW+W+++UYPPvjg5Q5dx44d06JFi7Rp0yYlJCQoLS1Nfn5+ql+/vh555BE9+uij8vK6/uWkWrVq2dx9u379eut3hTZt2mjy5MnWdX/++ack6ffff7fpAW80GuXn53fdcwXw70cRHQCuMW9vb9WqVeuycYGBgQ6Xf/LJJ0pISNC2bdtUqlQpbdy4URs3blT58uXVv39/SdKiRYtUv359JSQk2G0fEBAgf39/m2W33nqrTeE2NzfXOhSLJH311VcqU6aMzTbZ2dm69957rfNms1nnz5/X+PHjVbZsWZ06dUqS5OHhoalTp+qee+5Rbm6u4uLidP78eS1btkw7d+602WfNmjU1fPhwm2VLliyRwWBQRESEzGazunXrZrN+7ty5+vDDD/X999/bLPf29nb4+klSXFycevfurQMHDqhFixZq3Lix5syZo7Fjx+r8+fNaunSpJk+erOjoaI0bN+6SdwQURVZWlv7880/Vrl3buiwiIsLac/+nn35St27dbHryf/DBB3Y9/nfv3m0zVMzl/mAAAACA/OFZjh496nT98OHDNWnSpMvu5/jx42rTpo0aNGigr776SjfddJOGDh2qlJQULViwQKmpqWrUqJHeeecdDRgwQLt27VLPnj21bNky1atXz+E+L7yei4+Pt14v9uvXT6tXr7aJHTJkiM1dngMHDtTMmTNtYmJjYy97DVuYgvmFcnNzNXLkSE2bNk1Go1ERERFKSEhQ5cqV9dRTT2nLli3q3bu3Jk6cqCVLlhS600xh/Pzzz6pTp45NcX7t2rXKzs6WJPXt21e33XabBg0aJEkqUaKEwz9eXPhHA0maN2+eevfufc3yBPDfRREdANzMe++9J5PJpDvuuEORkZH68ccftX//fqWlpal///7atGmTDhw4IEk2xdoCkyZNsitU79u3T8HBwdb5JUuWaNiwYdb5hx56SJ999pnNNk8//bTNl5AlS5bY9aiW/hlm5fnnn9fIkSPl5+cnX19flSlTRn///bcyMzO1fv16tWjRQpUrV7bb/pNPPtEDDzygChUqSJK1uJ+amqq8vDydPn1aNWvWtOlBUrJkSZUvX97h67dy5Uo99thjuvfee7Vs2TLdcsstWrRokebMmaOnn35aRqNREyZM0LRp0zRs2DAdO3ZM8+bNc7ivoti3b5+6deumQ4cOae/evdae42XLllXNmjVlsVh08uRJSVJYWJik/B7pffr00d133y2LxWLzRejs2bMqVaqUJKl06dJXnR8AAMC/XWxsrM6fP+90/cUdTZwJCAjQa6+9pi5duig8PNzmGUULFy60/jxw4EANHDjQOl+/fn0tXrzYrlPIpVw8/rnBYCjUPq51T/SsrCx16NBBu3bt0ptvvqmBAwfK09NTNWvWVKNGjTRo0CANGjRIhw8fVrdu3XTvvfdq8+bNatiwYaGP1RGz2axXX31V48aNU58+ffT+++9b11WtWlV5eXmSpFKlSqlChQrW62gPDw8tXbpUkuyuo8+cOWO9fm7cuPFV5QcABSiiA8A1dv78eZvbL525cJzEi7Vs2VKzZ8+2zk+aNMk6P3LkSNWqVUs///yzXW/swMBA622iF6patardsguL6l988YXDnt3333+/9edHHnlEqampkvKHIHnuuefk7e2t+fPnq0WLFpLyL24ffvhhNWvWzHor7fbt27V+/XrNnTtXVapUsdn/Dz/8oG3btum5556TlF849/HxUfny5dW4cWObIv6FBfhevXpp/vz5dvkW9PTu3LmzFixYIA+P/Ed/HDp0SL6+vgoKCpKU/+Vk8ODB1n9bt27t8A8EhXHmzBlNnjxZr7/+uqpUqaINGzbYDb0SHBys48ePW+cLXuvw8HDt3r1bdevWVUxMjOLi4vT5558rNzdX9erV0zPPPGMz7A4AAACcq1GjhpKSkpSenm63rkqVKipZsmSh9uPh4aEnnnjCOh8dHa0xY8ZcdrvC7v9ayM3NtRtz3GKx2MxnZmbaxFzqmUVPP/20du/erdjYWGvhOTc3VwkJCTZDD1avXl3r169Xo0aN9Pjjj+vnn3+Wp6fnFR3D9u3bFRUVpR9++EEvvPCCJkyYYLN+zpw51jtxJWnDhg3WmJUrV+rRRx9Venq6wsPDNW/ePDVt2lQLFy7U2LFj9cMPP9jceQsAV4sHiwLANZaUlOTwYUYXT5caD92Z6dOna+fOnZoxY4bDond2drZKlChht/zIkSM6f/68dfr++++tBeoZM2Zo+/bt+uGHH2xinnzySZt9+Pj4qGLFioqNjdWIESP00ksvSZJefvlltW/fXr///rvOnTunzZs321ywJiQkyMPDw1rAvtDFt9N26dLF5uL5/fffl8VisZkef/xxp6/PgAEDVKVKFX344YfWArok7dmzRw0aNLBZJkn/93//J6PRqOnTpzvd56JFi+weBltwK+6mTZsUFhamqVOnavjw4frxxx9thsC50JYtW7R37175+vrKYrFo8eLF1nUnT57U66+/rpYtW0qSvLy8NG7cOA0fPlxr1651mhsAAABsXerBooMHD1bTpk2tU2pqqj788EObZRf3DJek119/XWXKlLnsVFQ7d+5UWFiYzSRJL774ot3yWbNm2Wz77bff6qabbrKZLn5gaGRkpM36yMhIh3ls3LhRn3zyiWbOnGnTc/vXX39VTk6OXW/zm266SdHR0dq3b5/1uUiO1KxZ0+Ya+sJhVR577DE1adJE3t7e2r59u9566y3rXZgXatGihSwWix566CHFxMTIYrHo1ltvta5/4403lJOTY827S5cu8vPzU+fOnS95VwIAFBU90QHgGivs+NXTpk3T4MGDi7TvXbt2adSoUfLx8dGnn35qd5unsyK6p6endXzBnTt36t5779Xu3bsVHh4uDw8PjR07Vh4eHlqzZo11m4vHWExKStLgwYO1du1arVixQiaTSaVLl9Zvv/2mkSNHqlu3bho4cKByc3MVGxtrva1y3759uvXWW+2K/tu3b9eXX355yePNzMy0eTBQwTE6epBRQa/2zz77TD4+Pjbrdu7c6fABrZ6enmrZsqU++ugjZWdnO+zFHxkZqTfeeMNmWUEv/oMHD2rw4MEaOHCgypUrd8ljccZisahfv36qXLmynnnmGevyrl27avPmzerZs6f279+vgICAK9o/AADAf8mlHix69uxZ3Xbbbdb5ffv2qVGjRnr44Yetyy4s0BYYMWKE9YGWl3KpZ/YcPXrU7vq6Vq1aDu+ulKQFCxZo/fr1WrRokSTZ3dHpaDiXC13cK11yPpzLtGnTVKdOHZve95K0Y8cOSdIdd9xht03btm0lSd99953154utX79eoaGh1vny5ctr/fr1kvKv6bdu3eq0A0ph7Nq1S1OmTNF7771nvY4vUaKEli5dqttvv10TJ04s1P83ACgMiugA4IbmzJmjOXPm2CyrUqWKPvroI3l6euqjjz5Sv379VKNGDWuvC7PZrOzsbIc9OAqYTCYFBwfrtttu05w5czR69GilpKRo48aNWrhwoUwmk8qVK+ewIBwXF6c//vhDcXFxqlmzpkaOHKkSJUqoRIkSeuuttzR8+HA1btxYDz30kDZs2KC9e/eqUaNG2rt3r91YhHl5eRowYIAqVap0ybEpBw8e7PAPDb169bJbtnHjRnl6etr1sNmzZ4/++usv65AzF6tcubIsFotSUlIUEhJit75cuXJOHxT77LPPasSIEU7zv1DBeJR5eXny8vKSxWLR7bffrldeeUXr16/Xtm3blJWVZe3FtHnzZj366KNauXKlXnzxResXKAAAADj36KOPatmyZXbLt2zZotatW9sse+WVV9SwYcPLPngyJiZGr7/++lXlFRQUpNjYWEnSn3/+qfbt26t8+fI2Rf0LffPNNypZsqSaNm162X1HR0dfMr9z58457Ggj5X+HiI2NVVRUlN26NWvWqH79+goMDLRbVzDU4oVDFl6sevXq1t71F1u+fLnDjjEX27Rpk7y8vGQ2m7Vy5Uq98sorysvLk8lk0jPPPKPu3bvroYcessbn5uZq06ZNGjJkiGJiYvTYY4+pbt26l20HAC6H4VwAwA117dpV+/fvt04FF7VeXl7WWyE7deqkrl276ty5c5Lyx+aW5PRW0qysLIWEhCgkJEQ//fST3n//fYWEhCg8PFxms1mPP/64QkJCNGXKFIfbR0ZGavfu3apZs6Z+/fVXvf7661q9erV1fWBgoMaPH6+3335b7du314IFC5SXl6ctW7Y47J1S0EvkUmMVFmU4F5PJpMDAQLuxKJctWyZfX1+7L00FCsaJvJJbcIvim2++0e7du+Xr66vc3FxrUfz+++/Xp59+qkWLFqlHjx7W+KFDh2rlypWaOnWqatSocV1zAwAA+LeYMWOGzXV0wRQeHn7F+3z55ZeVm5t72elSvL29VatWLdWqVUvVqlWTlF/wjY+PdzidOHFCOTk51vmcnJxL7v/uu+/WsWPHbKYlS5Zc9thOnDih7Oxsu2copaWl6euvv1bHjh0dbldc19DNmzdXbm6uOnbsqFdffVW5ubm69dZbVbFiRT333HN67bXXdPPNN2vPnj2SpLVr12rgwIF69tln1bZtW6d/PACAoqInOgBcY45u1SwqPz8/m97PjobymDZtmsLCwjR+/HjFxMQoIyNDkpwOK2I2m60PBi1gNBr17rvvOh0f8UKzZ8+2ebCPJLvxEe+991716tVLvXv31mOPPabGjRsrKytL7du3t4nz9PTUN99843D4lAv17dtXffv2tVvuqCe6p6en3W2r58+f19y5c9W5c2enF/hbt25VUFCQbrrppkvmcrWaNWtm/bng/AgPD7cW98PCwlSnTh1t3rxZFotFe/bs0Xvvvafw8HA99thj1zU3AACAfwOz2Syz2ezwui8rK6tYH/xZGH/99ZfNNaIjtWvXliTt37/f6d2RUv7zi26++WabZRUqVLhsDgUPBb34Ovqjjz5SVlaW3XOSCmzdulWSrnsv79jYWOu18/Lly613gZYoUcI6VEunTp00dOhQbdy4UZMnT1bbtm0VFBSk5cuXX9fcAPy30BMdAK6xoKAgh71fLp4KOwyIM5UqVdLIkSOtRfPTp09Lyi/AO/Laa68pICDAZsrOztZTTz1lt9yRxx9/XH/++adWr14tDw8PrV+/Xn/++ac+/PBDeXl56bfffrOOQdmhQwfdfvvt6tu3r3r06KHy5cvb7e9yBXQpvyfR6dOnbSZnBeV69erpr7/+snmg0gcffKDjx49r0KBBDrdZu3atfvnlF4fjpV+pn3/+WUOGDNGpU6esy44ePapz587p22+/lZR/S+25c+e0bds2a8wtt9yiPn36qEePHhowYIAiIiKuqscUAADAf82pU6esd15ePBX1WUQXevXVV+0eNO9oKiqj0SiLxaL33ntPy5Yts7n78s0331RwcLB1/lIFdCl/bHJvb2+bqeCh9Zfi7++vypUrW3tyS/njlb/11ltq1aqVbrnlFrttzGaz3nzzTfn4+KhDhw5FPm5nxo4dqxUrVljn+/bta71ubtWqlUaOHGmdv7CTzpgxY7Rz5061bNlSW7Zs0ahRo65ZTgBQgCI6AFxjF96qeanJ0diCRRUdHW0txhf0Mnc2PMro0aOVkZFhnQoeIjp27FidOHHCZp0jZcuWVdWqVfXmm2+qbdu2at26tapWrarvv/9eDzzwgOrUqWPT+6VHjx46d+5coYrlzpw/f15ZWVk2U15ensPYRx55RCVKlNCbb74pSUpOTtbo0aPVoUMH3XPPPXbxP/74o5588kn5+flp+PDhV5zjxUwmk6ZMmaLExERJ+Q+wOnfunLKysqy34RYcy9mzZ21uzZ0yZYp8fHx08OBBTZ8+/ZrlBAAA8F+ycuVKm2vbwhSTL3TixAnrMCqff/659u/fr5kzZ0qStm/fbtcxpmzZstb5hg0bWrctrJSUFHXv3l3ffPNNkfK8UNOmTW2O+cLr/cvp0aOHFi9eLJPJJEmaMGGCjh07pldffdUuNjc3VwMGDNCOHTv0f//3fwoKCrrinC+2Zs0affTRR5LyC/UZGRnW62az2azc3FzrfGZmpnW7mjVrKiYmRt9884169eqlO++885rlBAAFGM4FANzQ33//bXPhffEwLI4cO3ZMnp6eTnuS+/j4yMfHR1J+T+gJEybo7rvv1ldffaV33nlHAwcOVFRUlMqWLeu0jVOnTsnf31/Lly9X48aN9eijj2r+/PnasGGDTdzBgwc1ZswYNW/eXO+++64qVqyo8ePHF+bQbQwZMkRDhgyxW+5oOJeAgACNGjVKI0eO1PHjx7Vz505ZLBa9/fbbNnFJSUl6++23NXXqVHl7e+urr76S0Wi8bC6HDh3SV199pWXLltk99PVCBcXzgttnBwwYYP0yUODCoWPef/99PfPMM5LyC/snTpyQxWLRmDFj9P7777vdbccAAADurkSJEjZDuhQMWVJYM2fOtA4VcjFHnTOkf4ZdudCFQ6RcasjHkSNH6rffflOPHj3066+/FmoYlovl5uZaxykvcGGh+VJGjBihTz75RA888IDuvPNOLV68WEOGDNEdd9xhjcnLy9P69es1ZswY7d69Wx07dlRMTMxl952dna2vv/5aX375pSRd8kGpiYmJ1getJiQkWMeOLxAbG2t9gGpwcLC16J+bm6tvvvlGHh4eWrlypTZv3qwHH3ywUMcOAIVFT3QAuMbOnz/v9AFBF04pKSlO9/HZZ5+pdu3a1snZwz4v9OuvvyokJOSSXxIsFos2b96se++9V8ePH9fSpUsVFxen1157TbNmzVL16tV1+PBhp9tXqFBBn3/+uY4dO6bq1asrOjpaBoNBO3futF6k//bbb2revLnuu+8+ff3114qJidGECRPUr18/60NQC+PIkSN2DxUtmObPn+9wmxEjRmj48OFatWqVKlSooI0bN1ofkvTKK6/orrvuktFo1KRJk9S0aVPt2rVLLVq0cLiv7OxsZWdna9u2bapbt67CwsL07rvvqkWLFtbXOT4+XmfPnpXFYlFubq6SkpL01VdfqVSpUqpcubIkafr06UpOTlZycrL1QawF88nJydYHpX788cdq27atXn75ZX377bfasGGD7r33Xu3cubPQrxkAAACu3tixY+2uP3v37q1GjRrZLZ83b54kObxmvZCjIR+Dg4Ot62fNmqWXX375ip/Ts2PHDrshbAr7XB1/f3+tXbtWZcqU0bp16zR8+HBrsXr37t3q3Lmzbr75ZkVEROiPP/7Q5MmT9eWXX8rb29vh/tLS0iTlD8fi7++vRx55RCdOnNBjjz1m/a7y448/Ki8vTxaLRZmZmdqwYYOSkpJUo0YNSVJISIjNNfODDz6oIUOGWOf37t0rKb+zUZs2bbRv3z7t27dPHTp0UOvWrTVu3DidPXv2il5LAHCEnugAcI0lJSU57IlSFM8995xmz559yZht27Zpx44dKlu2rM6cOaO5c+eqefPmTuNzc3N1zz33aM+ePerZs6cmT55sHfrlmWee0WOPPabY2FjNnDlTN910kzZt2qRbb73VZh8Wi0W7d+/We++9p+XLl2vo0KGqUKGCXnvtNcXHx6tJkyYaOnSobr/9dn388ccyGAyKjo7W2bNnNWHCBH3//ffavXv3JYd4eeutt/TWW28V6nWaN2+eevfubZ03GAyaNGmSJk2aZBdbq1Ytbdy4UUOHDlX37t11++23O32d2rRpo++//17Z2dmqVq2aWrVqpQ8//FB33XWXNa5p06ZasGCBli1bZreP3r17y8Mj/+/U5cuXt44JX9Cr6MJhb3799Ve9/PLL+vrrrzV79mzrw5t27typJ598Unfffbe6dOmiTz75RF5efGwDAABcTqtWreyWObqTsTB++eUXTZ06VUuWLNH69euvaB8VKlRQvXr1LjmuuZ+fnx5++GGtXLlSZ8+eVWxsbKGv/W666SZ17dpVn376qd26vLw8vfLKKypTpox27dolyfGziW677Tb9+OOPdsvr16+vkydP6oEHHlBERIQeffRRhw9ulaTFixdrxIgROnr0qHx9feXr66upU6fq0Ucftf5xYP/+/SpRooQaN25st33p0qXVsWNHSfl3D1x4zezj46MyZcpYl2VnZ2vWrFkaNWqU6tatq++//17+/v6aP3++GjZsqFdeeUVz5szRvHnz1KZNm8u8ggBweXwbB4BrrEqVKjpy5Mhl46ZNm+bwAUdvvPGG0wvTC505c0avv/66MjIydP78edWsWfOSD9Hx8vLS/PnzVbZsWYWGhtqtL1eunDp16qTPPvtMa9asUenSpfXss89a1587d06NGjXSoUOH1LZtW3333XfW8QafeOIJ5eTkqGXLlnryySc1bdo0m54p48ePV2hoqI4cOXLZMdL79OmjYcOGXfb4JVl7exfGE088oSeeeOKycV5eXmrcuLHatm2rDh06OP2DyAcffKA+ffrIZDIpOztb3t7eKl++vGrWrKk6deoUKqfc3Fw999xzqly5svbv368qVapY11WrVk1btmzRokWL9Ndff1FABwAAKKQPP/zQZtiVvn37Fnkf2dnZuv/++/Xzzz8rMjJSe/bssetgUliPPPJIoR5kf+zYMfXt21d///23PDw8FBUVVaj9X+r5Pp6entq5c6eOHDkis9msJ554wunwj474+voWeqz2WrVqqXXr1oqIiFDLli1VunRpu5jatWtr//792rNnj7XHeokSJVS5cmU1atRI5cqVK1RbGzZs0KuvvqrJkyerd+/eNkPlvPjii+rSpYvGjRun+vXrF2p/AHA5BsvF9xgBAODE77//rooVK8rf39/h+r///lt+fn7Fm9QNLjc3lwI5AADANWCxWHT8+HFVqFDB+iygS0lNTVXJkiWddmBJTk6Wn58fz6hxU1xHAyhOFNEBAAAAAAAAAHCCB4sCAAAAAAAAAOAERXQAAAAAAAAAAJygiA4AAAAAAAAAgBMU0QEAAAAAAAAAcILHGF8DZrNZSUlJKlu2rAwGg6vTAQAAwL+ExWJRRkaGgoKC5OHx3+3/wvU2AAAArofCXm9TRL8GkpKSFBIS4uo0AAAA8C917NgxGY1GV6fhMlxvAwAA4Hq63PU2RfRroGzZspLyX+xy5cq5OBsgv7dWamqqAgIC/tO91gCgqHj/hLtJT09XSEiI9Xrzv4rrbbgbPi8A4Mrw/gl3U9jrbYro10DBLaXlypXjoh5uwWw2KysrS+XKleNDCQCKgPdPuKv/+hAmXG/D3fB5AQBXhvdPuKvLXW9ztgIAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAC4YZlMJhmNRrupZMmSateunSQpJydHw4YNU9WqVRUcHKy77rpL3377rd1+unbtqipVqqhRo0bq1KmTjhw5Yl1//vx5jRs3TmFhYapUqZLuuecefffdd3b5HD58WA899JAqV66soKAgde3aVcnJydf1NQAAFN7Ro0fl5+en3r17O41JTU3VRx99pPvuu0/VqlWzW3/+/HmNHz9e99xzjypXruz0M6EobS5fvlwGg0Hz588vwtEAAIDiQhEdAADcsIxGo0wmk83022+/qVSpUhoyZIgkqX///vrxxx8VFxenxMRERUdHq3379jp06JCk/GJIy5YtVbVqVR08eFA//PCDatasqfbt2ys3N1eSNH78eH355Zf69ttvdfz4cb3yyit65JFHFB8fb83l9OnTuv/++9W0aVOZTCYdPnxYvr6+mjFjRvG/MAAAO2azWT179lSVKlUuGdeqVSutW7dOoaGhslgsduvHjx+vr776Sl988YWSk5MdfiYUpc3k5GQ9//zzqlGjRtEPCgAAFAuK6AAA4F8lJiZG9957r1q2bKmcnBz9+uuv+uCDD+Tv7y9J6tSpk2rVqqXVq1dLkuLj41W5cmVNmjRJ3t7e8vT01KhRo7R//37t27dPkjRv3jyNGDFCwcHBkqSIiAg98cQTeuedd6ztTpkyRdWrV9dLL70kT09PlShRQvPmzVNMTEwxvwIAAEdee+01lStXTp06dbpk3I8//qjFixerZcuWDtfPmzdPw4cPV+XKlSU5/kwobJsWi0W9evXS//3f/8loNBbxiAAAQHGhiA4AAP41kpOT9fbbb2vixImSJB8fH+3cuVOhoaHWmIyMDB05ckTlypWTJNWvX1+bN2+WwWCwxvzyyy+SpLJly0qS0tLS5OFhe9lUsmRJbdmyxTq/cuVKuyKJp6fnNTw6AMCV2rVrl6ZPn65Zs2Zd9b4K85lQ2DanTJmiv//+Wy+++OJV5wUAAK4fiugAAOBfY+rUqXrwwQfVoEEDh+tTUlIUERGhm2++WV27dnUY89NPP6lr167q3bu3dSzc7t27a+LEiTp06JDMZrPWr1+vxYsX66+//rJud/DgQQUGBuqpp55StWrV1KBBA7366qvWIWEAAK6RmZmpHj16aNq0aTZ/VL1S3bt312uvvaYjR444/UwoTJs//vijXn31VX388cfy8vK66rwAAMD1QxEdAAD8K/z999+aPXu2hg4d6nD95s2b1bBhQ/n5+em7775TyZIl7WLefvttPfzww+rVq5c++OAD6/KZM2eqU6dO6tChg6pXr65PP/1UI0eOtCl65OXlacyYMerRo4cOHz6szz//XIsXL9bw4cOv/cECAApt0KBBuuOOO/T4449fk/3NnDlTDz/8sHr27KmwsDCHnwmXa/PcuXPq0aOHXnvtNd1yyy3XJC8AAHD9UEQHAAD/Ch9//LEqVqyo+++/327dBx98oM6dO+u1117TihUrrOOjFzCbzXr66af1zjvvaNmyZZo4caLNUCw+Pj4aO3as9u/fryNHjmjevHlKSUmxeQhcaGioevfurZYtW8pgMOiWW27RqFGjtGDBgut30ACAS1q6dKm+/vprvfvuu9dsnz4+PhozZoy2bNmiw4cP230mFKbNoUOHqlq1aurXr981ywsAAFw/3DMGAAD+FebOnauePXvajG0uScuXL9eYMWO0detW1alTx+G2L730kg4cOKBdu3YpKyvLbn1eXp7y8vLk4+Njs9/OnTtb55s1a6acnBy7bX19fa/0kAAAV2n16tVKTExUhQoV7NZ99NFH2rhxo9MHiDqTl5en8+fP2yy78DOhMG2uXr1aR48etfvM+vbbb9WnTx+dP3+eIV4AAHAj9EQHAAA3vN9//10//vijIiIibJZnZmbq2Wef1SeffOK0gL5z504tWLBAX331lfVhoxfr2bOn+vfvr+zsbOXl5enVV1/VyZMnNXDgQGtMdHS05syZo6+//lqSdOzYMU2YMEFPPfXUNTpKAEBRzZ8/XxaLxWYaM2aMevXqJYvFUuQCupT/mTBgwACnnwmFafPIkSN2Mffff7/mzZsni8VCAR0AADdDER0AANzwVq9eLT8/P4WHh9ssj4uLU2pqqh5//HEZjUabqUuXLpKkdevWKTMzU7fddptCQ0PVqFEjhYaGymg0asqUKZKkt956S2fOnFG1atUUEhKiPXv26JtvvlGZMmWsbYWFhemzzz7TyJEjFRgYqAceeEBdu3bV6NGji++FAAAUiclkktFo1NKlSwu9TcFnwl133aUqVao4/EwAAAD/LgaLxWJxdRI3uvT0dJUvX15paWlOe7ABxclsNislJUWBgYHy8OBvZQBQWLx/wt1wnZmP1wHuhs8LALgyvH/C3RT2OpOzFQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAkvVycAAICrnFm82NUpwM2YJWV5eupMXh49DWCndPfurk4BLrAic4WrU4Abspgt0jlJmZLBw+DqdOBGOpbp6OoUAADXAd8PAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOuKyIbjKZZDQa7aaSJUuqXbt2kqTs7GxFR0crLCxMQUFB6tixoxITE232k5iYqK5du6pq1aoKDg7W4MGDlZ2dbROzY8cONWvWTKGhoapZs6bee+89u3zmz5+vevXqyWg0qnHjxtq6dev1O3gAAAAAAAAAwA3BZUV0o9Eok8lkM/32228qVaqUhgwZIkkaMGCAtm/frt27dyshIUFhYWFq166d8vLyJEk5OTlq1aqVjEajDh48qN9++01xcXEaPHiwtZ34+Hi1bt1aL774ohISErR8+XKNHj1aS5YsscYsXLhQI0aM0NKlS2UymTRs2DBFRETo8OHDxfuiAAAAAAAAAADcilsN5xITE6N7771XLVu2VEJCgubPn68pU6bIz89PXl5emjRpkpKSkrR69WpJ0pIlS3T8+HHFxMTIy8tLfn5+mjp1qubOnasTJ05IkiZPnqz7779fnTt3liTVqVNHL730kiZNmmRtd9y4cRo6dKhq164tSerSpYvuu+8+zZw5s5hfAQAAAAAAAACAO3GbInpycrLefvttTZw4UZL0zTffqFKlSgoPD7fG+Pj4qHXr1lq7dq0kadOmTWrTpo18fHysMeHh4fL391dsbKw1JjIy0qatyMhI7d27V8ePH1dCQoIOHTrkMKagHQAAAAAAAADAf5OXqxMoMHXqVD344INq0KCBJCkpKUlBQUF2ccHBwfr999+tMfXq1XMYUzB2uqP9BAcHS8ofTz0nJ0eSHMZcPP56gezsbJtx19PT0yVJZrNZZrP58gcLXGdms1kWi4XzEbgMfkNwMbMkizg34JgrPlf5LAcAAABczy2K6H///bdmz56tFStWWJd5e3vLw8O+o7zBYLjqmIvXS7pkzMViYmI0btw4u+WpqanKyspyuh1QXMxms9LS0mSxWBz+jgDIl+Xp6eoU4GYsktL/977p/EoA/1VnUlKKvc2MjIxibxMAAACALbcoon/88ceqWLGi7r//fusyo9GopKQku9jk5GRrT/IrjUlOTpb0T490Kb/HelhYmMN9XGzEiBGKioqyzqenpyskJEQBAQEqV67cZY8XuN7MZrMMBoMCAgIoogOXcOZ/D6oGChT0+a2Ql+c+Y97BbZQODCz2NkuUKFHsbQIAAACw5RZF9Llz56pnz542vb+bN2+ulJQU/fzzz9YhXvLy8rR582bNmjVLktS2bVv17dtXubm58vLKP5T4+HilpKSoRYsW1pg1a9bomWeese5748aNatiwoSpVqiRJatiwodasWaPnn3/eJqZdu3YO8/X19ZWvr6/dcg8PDwqWcBsGg4FzErgMfjvgiEH55wbnBy7mis9UPscBAAAA13P5Vfnvv/+uH3/8URERETbLAwIC1KdPH0VFRSk9PV15eXkaOXKk/Pz81L59e0lSRESEAgMDNWrUKOXl5SktLU2DBg1Snz59VLFiRUnSwIEDFRsbax0q5sCBA5o4caKGDx9ubWvYsGF64403dODAAUnS8uXLtXbtWg0YMKA4XgIAAADghnX06FH5+fmpd+/eTmMSExPVtWtXVa1aVcHBwRo8eLDNM4YAAAAAd+byIvrq1avl5+en8PBwu3UzZsxQ/fr1VadOHRmNRu3fv1/r1q2z9jr38vLSunXrtG/fPoWEhKhu3bqqX7++pk+fbt1HWFiYVq1apQkTJig4OFgREREaPXq0unXrZo3p3r27Ro0apQ4dOigoKEivvvqqVq1aZTO8CwAAAABbZrNZPXv2VJUqVZzG5OTkqFWrVjIajTp48KB+++03xcXFafDgwcWYKQAAAHDlDBaLxeLqJG506enpKl++vNLS0hgTHW7BbDYrJSVFgYGB3AYOXMKZxYtdnQLcjFnSSU9P+TMmOhwo3b17sbfp7teZEydO1I4dO3THHXfoyJEjmj9/vl3Mxx9/rBdeeEHJycny8fGRJMXFxalJkyZKTEy03kF6Ka58HVZkrijW9nBjsJgt0klJ/pLBg0dR4x8dy3R0dQqAW6NeAXdT2OtMzlYAAAAARbZr1y5Nnz7d+rwiZzZt2qQ2bdpYC+iSFB4eLn9/f8XGxl7vNAEAAICr5hYPFgUAAABw48jMzFSPHj00bdo0hYaGXjI2KSlJ9erVs1seHBysxMREh9tkZ2fbjJmenp4uKb/3mtlsvorMi85i5sZdOGCWZMn/1yLOEfyjuN+jgBuN2WyWxWLhdwVuo7DnIkV0AAAAAEUyaNAg3XHHHXr88ccvG+vt7e3wdm2DwfkQGDExMRo3bpzd8tTUVGVlZRUt2at1rnibww3CIinjfz8zmgsukHI2xdUpAG7NbDYrLS1NFouF4VzgFjIyMi4fJIroAAAAAIpg6dKl+vrrr/XLL78UKt5oNCopKclueXJysoKDgx1uM2LECEVFRVnn09PTFRISooCAgOIfGz6zeJvDDaKg01oFMUgqbASWCXR1CoBbM5vNMhgMCggIoIgOt1CiRIlCxVFEBwAAAFBoq1evVmJioipUqGC37qOPPtLGjRvVsmVL67K2bduqb9++ys3NlZdX/teP+Ph4paSkqEWLFg7b8PX1la+vr91yDw+PYv/CzUMj4YhFlvwe6B6cI7BFURC4PIPB4JLPdMCRwp6HnK0AAAAACm3+/PmyWCw205gxY9SrVy9ZLBabArokRUREKDAwUKNGjVJeXp7S0tI0aNAg9enTRxUrVnTRUQAAAACFRxEdAAAAwDVjMplkNBq1dOlSSZKXl5fWrVunffv2KSQkRHXr1lX9+vU1ffp0F2cKAAAAFA7DuQAAAAC4KmPHjrX+bDQaZTKZbNYbjUYtX768mLMCAAAArg16ogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAokrS0NPXr10+hoaEKDQ1VeHi4vvjiC6fxkZGR8vf3l9FotE7NmjUrxowBAACAK+fl6gQAAAAA3Fi6dOkio9Goffv2qUyZMtq0aZMiIyMVHBysu+66yy7eZDJp8eLFat26tQuyBQAAAK6OS3uiHz58WA899JAqV66soKAgde3aVcnJydb12dnZio6OVlhYmIKCgtSxY0clJiba7CMxMVFdu3ZV1apVFRwcrMGDBys7O9smZseOHWrWrJlCQ0NVs2ZNvffee3a5zJ8/X/Xq1ZPRaFTjxo21devW63PQAAAAwA1u0aJFmj17tsqUKSNJat68ucLCwrRt2zaH8SaTSSEhIcWZIgAAAHDNuKyIfvr0ad1///1q2rSpTCaTDh8+LF9fX82YMcMaM2DAAG3fvl27d+9WQkKCwsLC1K5dO+Xl5UmScnJy1KpVKxmNRh08eFC//fab4uLiNHjwYOs+4uPj1bp1a7344otKSEjQ8uXLNXr0aC1ZssQas3DhQo0YMUJLly6VyWTSsGHDFBERocOHDxffCwIAAADcIAICAuTj4yNJysrK0pw5cxQfH6+mTZvaxWZnZ+vEiRMyGo3FnSYAAABwTRgsFovFFQ2PGjVK3333nb799lvrsry8PHl6ekqSEhISVK1aNe3atUvh4eGS8ovmQUFB+vDDD9WxY0d9/PHHeuGFF5ScnGy9iI+Li1OTJk2UmJioihUr6plnntHx48e1cuVKaztvvfWWFi1apD179kiSwsLC1L9/fw0ZMsQaExkZqZo1a2rKlCmXPZb09HSVL19eaWlpKleu3NW/OMBVMpvNSklJUWBgoDw8ePQB4MyZxYtdnQLcjFnSSU9P+efl8eAY2CndvXuxt+nu15lGo1FJSUlq0KCBJk6cqA4dOtjFHDp0SA0aNNDYsWP1ySefKC0tTffcc49iYmIUGhrqcL/Z2dk2d5emp6crJCREp0+fLvbXYWXmyssH4b/HLOmUpAriSWOwEVkm0tUpAG7NbDYrNTVVAQEB1CvgFtLT03XTTTdd9nrbZWOir1y5Ur1797ZZVlBAl6RvvvlGlSpVshbQJcnHx0etW7fW2rVr1bFjR23atElt2rSxFtAlKTw8XP7+/oqNjVXXrl21adMmRUdH27QTGRmpoUOH6vjx48rOztahQ4cUGRlpFzN16tRCFdEBAACA/yKTyaTTp09rypQpmjt3rh544AHrEC8F0tLSVLFiRQUFBWnbtm0ym816+eWX1bx5c/30008qXbq03X5jYmI0btw4u+WpqanKysq6bsfj0LnibQ43CIukjP/9bHBlInA3KWdTXJ0C4NbMZrPS0tJksVgoosMtZGRkXD5ILiyiHzx4UIGBgXrqqae0efNmlS1bVl27dtXw4cPl5eWlpKQkBQUF2W0XHBys33//XZKUlJSkevXqOYwpGDvd0X6Cg4Ml5Y+nnpOTI0kOYy4ef72Ao54xUv4bgdlsLtTxA9eT2WyWxWLhfAQug98QXMys/LoI5wYcccXn6o3wWX7TTTdpwoQJatKkiWbOnGnXgaVRo0Y6evSozbKCovuWLVvUtm1bu32OGDFCUVFR1vmCnugBAQHF3yM/s3ibww2i4FeTnui4SGCZQFenALg1s9ksg8FAT3S4jRIlShQqzmVF9Ly8PI0ZM0bvvvuu5s6dqz/++EOPPPKITp06pbfeekve3t4Of5kMhn/+zH+lMRevl3TJmIu5Vc8YwAH+sgsUTtYFd0ABUn4BPf1/75t0LMTFzqQUf+/CwvaMKU5ms1lr1qyxG7qlYsWK+uuvv5xuc+E1ScEf+51dc/v6+srX19duuYeHR7Ff2xg8eDeAPYss+R8UHpwjsMX3L+DyDAaDSz7TAUcKex66rIgeGhqqJ598Ui1btpQk3XLLLRo1apQGDRqkt956yzq+4sWSk5OtPcmvNCY5OVnSPz3Spfwe62FhYQ73cTG36hkDOMBfdoHCOfO/B1UDBawdCxkTHQ6UDiz+3oWF7RlTnFJTU/X0009rwIABio6Olq+vr9avX6/169dr1apVdvFbtmzR008/rUWLFqlx48bKysrSkCFDZDQa9cADDxT/AQAAAABF5LIierNmzaxDqVyooMdJ8+bNlZKSop9//lkNGjSQlN97ffPmzZo1a5YkqW3bturbt69yc3Pl5ZV/KPHx8UpJSVGLFi2sMWvWrNEzzzxjbWPjxo1q2LChKlWqJElq2LCh1qxZo+eff94mpl27dg5zd6eeMYAz/GUXuDx+O+DI/zoWcn7Ajis+U93xc7xSpUrasWOHoqOjVb16dVksFlWqVEnz589Xq1atZDKZdPfdd2vq1Knq0qWLmjVrppdfflnPPfec9ZlEzZo104YNGxxeUwMAAADuxmVF9OjoaDVt2lTNmjVTy5YtdezYMU2YMEFPPfWUJCkgIEB9+vRRVFSUvvjiC5UuXVojR46Un5+f2rdvL0mKiIhQYGCgRo0apYkTJyozM1ODBg1Snz59VLFiRUnSwIEDFR4erhUrVqhjx446cOCAJk6cqMmTJ1tzGTZsmF566SW1bdtWt9xyi5YvX661a9cqLi6u+F8YAAAAwM1Vq1ZNn332mcN1RqNRJpPJZlnv3r3Vu3fvYsgMAAAAuPZcVkQPCwvTZ599pmHDhqlHjx4qW7asevfurREjRlhjZsyYoejoaNWpU0d5eXm68847tW7dOmuvcy8vL61bt04DBw5USEiIPDw81KVLF02aNMmmnVWrVikqKkr9+/dXqVKlNHr0aHXr1s0a0717d6Wnp6tDhw7KzMyU0WjUqlWrbIZ3AQAAAAAAAAD89xgsFovF1Unc6NLT01W+fHmlpaUxJjrcgtlsVkpKigIDA93yNnDAXZxZvNjVKcDNmCWd9PSUP2Oiw4HS3bsXe5tcZ+Zz5euwInNFsbaHG4PFbJFOSvLnwaKw1bFMR1enALg16hVwN4W9zuRsBQAAAAAAAADACYroAAAAAAAAAAA4QREdAAAAAAAAAAAnKKIDAAAAAAAAAOAERXQAAAAAAAAAAJygiA4AAAAAAAAAgBMU0QEAAAAAAAAAcIIiOgAAAAAAAAAATlBEBwAAAAAAAADACYroAAAAAAAAAAA4QREdAAAAAAAAAAAnKKIDAAAAAAAAAOAERXQAAAAAAAAAAJygiA4AAAAAAAAAgBMU0QEAAAAAAAAAcIIiOgAAAAAAAAAATlBEBwAAAAAAAADACYroAAAAAAAAAAA4QREdAAAAAAAAAAAnKKIDAAAAAAAAAOAERXQAAAAAAAAAAJygiA4AAAAAAAAAgBMU0QEAAAAAAAAAcIIiOgAAAAAAAAAATlBEBwAAAAAAAADACYroAAAAAAAAAAA4QREdAAAAAAAAAAAnKKIDAAAAAAAAAOAERXQAAAAAAAAAAJygiA4AAAAAAAAAgBMU0QEAAAAAAAAAcIIiOuAm4uLi5O3tLaPRaDN9+eWXkqScnBwNGzZMVatWVXBwsO666y59++23NvswmUzq2rWrqlSpokaNGqlTp046cuSIdb3FYtHkyZN16623KiQkRLfccoteffVVmc1mm/3Mnz9f9erVk9FoVOPGjbV169brfvwAAAAAAACAO/JydQIA8plMJjVu3Fjbtm1zuL5///46duyY4uLi5O/vry+//FLt27fXzz//rBo1auj8+fNq2bKlHnroIS1YsEAnTpzQ1KlTrTFeXl6KiYnRF198oY0bNyo0NFRHjhxRmzZt5OPjo5deekmStHDhQo0YMUKbNm1S7dq1tXTpUkVERGjv3r2qXr16cb4kAAAAAAAAgMvREx1wEyaTSSEhIQ7X5eTk6Ndff9UHH3wgf39/SVKnTp1Uq1YtrV69WpIUHx+vypUra9KkSfL29panp6dGjRql/fv3a9++fZKkIUOGaP369QoNDZUkVa1aVW3btrXpaT5u3DgNHTpUtWvXliR16dJF9913n2bOnHndjh0AAAAAAABwVxTRATdhMplkNBodrvPx8dHOnTutxW9JysjI0JEjR1SuXDlJUv369bV582YZDAZrzC+//CJJKlu2rCTJ19fXWoQ3m83avHmzPv30Uz3wwAOSpISEBB06dEiRkZE27UdGRmrt2rXX5kABAAAAAACAGwhFdMBNmEwmnTp1Sg8//LCqV6+uxo0b68MPP3QYm5KSooiICN18883q2rWrw5iffvpJXbt2Ve/evVWtWjWbdd26dVOJEiXUvXt3DRs2TC+++KIkKSkpSZIUFBRkEx8cHKzExMSrPEIAAAAAAADgxkMRHXATBoNBKSkpmjZtmg4dOqRZs2bplVde0Zw5c2ziNm/erIYNG8rPz0/fffedSpYsabevt99+Ww8//LB69eqlDz74wG79p59+qszMTE2dOlWff/654uPjJUne3t6SJA8P27eGC3u3AwAAAAAAAP8lFNEBN7FgwQKtXr1aVatWlcFgUOPGjfXCCy9o3rx51pgPPvhAnTt31muvvaYVK1ZYh2YpYDab9fTTT+udd97RsmXLNHHiRHl6ejpsz8fHR927d9eDDz6owYMHS5J1OJmCHukFkpOTFRwcfC0PFwAAAAAAALghUEQH3ITZbLZblpeXZ+0Fvnz5co0ZM0Zbt25V7969He7jpZde0oEDB7Rr1y41atTIbv3mzZuVkZFhs6xixYr666+/JEmVKlVSw4YNtWbNGpuYjRs3ql27dldyWAAAAAAAAMANjSI64CYiIiL00ksv6ezZs5Kk3bt3a9q0aerbt68yMzP17LPP6pNPPlGdOnUcbr9z504tWLBAX331lfVhoxeyWCwaP368nnjiCaWmpkqSDh48qHfeeUcdOnSwxg0bNkxvvPGGDhw4ICm/eL927VoNGDDgWh8yAAAAAAAA4Pa8XJ0AgHzvv/++Xn75Zd16663KyclR+fLlNXbsWD311FP69ttvlZqaqscff9xuu3vuuUdLly7VunXrlJmZqdtuu01Sfs/2grHNo6KiFBUVpVWrVmnMmDG68847lZOTo5IlS6pnz556+eWXrfvr3r270tPT1aFDB2VmZspoNGrVqlUKCwsrnhcCAAAAAAAAcCMGi8VicXUSN7r09HSVL19eaWlpDnsAA8XNbDYrJSVFgYGBdg8JBfCPM4sXuzoFuBmzpJOenvLPy+N2Pdgp3b17sbfJdWY+V74OKzJXFGt7uDFYzBbppCR/yeBhcHU6cCMdy3R0dQqAW6NeAXdT2OtMzlYAAAAAAAAAAJygiA4AAAAAAAAAgBMU0QEAAAAAAAAAcIIiOgAAAAAAAAAATlBEBwAAAAAAAADACYroAAAAAIokLS1N/fr1U2hoqEJDQxUeHq4vvvjCaXxiYqK6du2qqlWrKjg4WIMHD1Z2dnYxZgwAAABcOS9XJ4Crt/RQmqtTgLuxmGXIOCNLZppk4G9lsNWlRnlXpwAAuMF16dJFRqNR+/btU5kyZbRp0yZFRkYqODhYd911l01sTk6OWrVqpXbt2mnRokXKzMxUx44dNXjwYM2aNctFRwAAAAAUHtU1AAAAAEWyaNEizZ49W2XKlJEkNW/eXGFhYdq2bZtd7JIlS3T8+HHFxMTIy8tLfn5+mjp1qubOnasTJ04Ud+oAAABAkVFEBwAAAFAkAQEB8vHxkSRlZWVpzpw5io+PV9OmTe1iN23apDZt2ljjJSk8PFz+/v6KjY0ttpwBAACAK8VwLgAAAACuiNFoVFJSkho0aKBly5apcePGdjFJSUmqV6+e3fLg4GAlJiY63G92drbNmOnp6emSJLPZLLPZfI2yLxyL2VKs7eEGYZZkyf/XIs4R/KO436OAG43ZbJbFYuF3BW6jsOciRXQAAAAAV8RkMun06dOaMmWK5s6dqwceeMA6xEsBb29veXjY3wBrMBic7jcmJkbjxo2zW56amqqsrKyrT7wozhVvc7hBWCRl/O9n56cy/oNSzqa4OgXArZnNZqWlpclisTi8PgCKW0ZGxuWDRBEdAAAAwFW46aabNGHCBDVp0kQzZ85UdHS0zfqC3uoXS05OVnBwsMN9jhgxQlFRUdb59PR0hYSEKCAgQOXKlbu2B3A5mcXbHG4QBZ3WKohBUmEjsEygq1MA3JrZbJbBYFBAQABFdLiFEiVKFCqOIjoAAACAQjObzVqzZo06dOhgs7xixYr666+/7OLbtm2rvn37Kjc3V15e+V8/4uPjlZKSohYtWjhsw9fXV76+vnbLPTw8iv0Lt8GDbsawZ5Elvwe6B+cIbFEUBC7PYDC45DMdcKSw5yFnKwAAAIBCS01N1dNPP61x48ZZxy1fv3691q9fr4iICLv4iIgIBQYGatSoUcrLy1NaWpoGDRqkPn36qGLFisWdPgAAAFBkFNEBAAAAFFqlSpW0Y8cO7du3T9WrV1dQUJCio6M1f/58tWrVSiaTSUajUUuXLpUkeXl5ad26ddq3b59CQkJUt25d1a9fX9OnT3fxkQAAAACFw3AuAAAAAIqkWrVq+uyzzxyuMxqNMplMdsuWL19eHKkBAAAA1xw90QEAAAAAAAAAcMKlRfS4uDh5e3vLaDTaTF9++aUkKTs7W9HR0QoLC1NQUJA6duyoxMREm30kJiaqa9euqlq1qoKDgzV48GDr2IwFduzYoWbNmik0NFQ1a9bUe++9Z5fL/PnzVa9ePRmNRjVu3Fhbt269fgcOAAAAAAAAALghuLSIbjKZ1LhxY5lMJpupU6dOkqQBAwZo+/bt2r17txISEhQWFqZ27dopLy9PkpSTk6NWrVrJaDTq4MGD+u233xQXF6fBgwdb24iPj1fr1q314osvKiEhQcuXL9fo0aO1ZMkSa8zChQs1YsQILV26VCaTScOGDVNERIQOHz5cvC8IAAAAAAAAAMCtuLyIHhIS4nBdQkKC5s+frylTpsjPz09eXl6aNGmSkpKStHr1aknSkiVLdPz4ccXExMjLy0t+fn6aOnWq5s6dqxMnTkiSJk+erPvvv1+dO3eWJNWpU0cvvfSSJk2aZG1r3LhxGjp0qGrXri1J6tKli+677z7NnDnzeh4+AAAAAAAAAMDNubyIbjQaHa775ptvVKlSJYWHh1uX+fj4qHXr1lq7dq0kadOmTWrTpo18fHysMeHh4fL391dsbKw1JjIy0mbfkZGR2rt3r44fP66EhAQdOnTIYUxBOwAAAAAAAACA/yYvVzZuMpnk5eWlhx9+WD///LP8/f3Vv39/PfXUU0pKSlJQUJDdNsHBwfr9998lSUlJSapXr57DmIKx0x3tJzg4WFL+eOo5OTmS5DDm4vHXC2RnZ9uMu56eni5JMpvNMpvNhTr2a8rigjbh3iyWfyZxfsCWS96n3BSvBC5mlsQ7J5xxxfsn79kAAACA67m0iG4wGJSSkqJ33nlHVapU0e7du/XQQw/p/Pnz8vb2loeHfUd5g8Fg/flKYy5eL+mSMReLiYnRuHHj7JanpqYqKyvL6XbXiyHjTLG3CXdnkeFchmSQ/vcfwColJfvyQf8RWZ6erk4BbsYiKf1/1wS8e+JiZ1JSir3NjIyMYm8TAAAAgC2XFtEXLFhgM9+4cWO98MILmjdvngYPHqykpCS7bZKTk609yY1G4xXFJCcnS/qnR7qU32M9LCzM4T4uNmLECEVFRVnn09PTFRISooCAAJUrV+6Sx3w9WDLTir1NuDmLRbJIljIVpEv8QQj/TYGB5V2dgts4878HVQMFCvr8VsjLc+2Yd3BLpQMDi73NEiVKFHubAAAAAGy5tIhuNpvteoDn5eXJYDCoefPmSklJ0c8//6wGDRpY123evFmzZs2SJLVt21Z9+/ZVbm6uvLzyDyU+Pl4pKSlq0aKFNWbNmjV65plnrG1s3LhRDRs2VKVKlSRJDRs21Jo1a/T888/bxLRr185h3r6+vvL19bVb7uHh4bBn/HVn4Gs+LmbOL54bDJwfsOOS9yk3xSsBRwzKPzc4P3AxV7x/8p4NAAAAuJ5Lr8ojIiL00ksv6ezZs5Kk3bt3a9q0aerbt68CAgLUp08fRUVFKT09XXl5eRo5cqT8/PzUvn176/aBgYEaNWqU8vLylJaWpkGDBqlPnz6qWLGiJGngwIGKjY3VihUrJEkHDhzQxIkTNXz4cGsew4YN0xtvvKEDBw5IkpYvX661a9dqwIABxflyAAAAAAAAAADcjEt7or///vt6+eWXdeuttyonJ0fly5fX2LFj9dRTT0mSZsyYoejoaNWpU0d5eXm68847tW7dOmuvcy8vL61bt04DBw5USEiIPDw81KVLF02aNMnaRlhYmFatWqWoqCj1799fpUqV0ujRo9WtWzdrTPfu3ZWenq4OHTooMzNTRqNRq1atshneBQAAAAAAAADw32OwWCwWVydxo0tPT1f58uWVlpbmkjHRlx5iTHRcxGKWIeOULGUrMJwL7HSpwZjoBc4sXuzqFOBmzJJOenrKnzHR4UDp7t2LvU1XX2e6C1e+DisyVxRre7gxWMwW6aQkf8ngwTOI8I+OZTq6OgXArZnNZqWkpCgwMJBh6+AWCnudydkKAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAAAAAAAAOEERHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAAAAAAAAOEERHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAAAAAAAAOEERHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAAAAAAAAOEERHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAACK5MMPP1S9evUUHBysWrVq6d13371kfGRkpPz9/WU0Gq1Ts2bNiilbAAAA4Op4uToBAAAAAMXn+PHjSk5OVm5uripVqqSQkJAibb9w4UKNGTNG69atU926dRUfH6/mzZurXLlyevzxxx1uYzKZtHjxYrVu3fpaHAIAAABQrOiJDgAAAPzLnT59WiNGjFBYWJiCg4PVqlUrRUZGqkaNGgoKClK/fv105MiRQu1rx44deuONN1S3bl1JUq1atdS9e3ctW7bM6TYmk6nIxXoAAADAXVBEBwAAAP7Fli1bpvDwcEn5vcizsrKUmpqq5ORkZWdna926dapevbpat26tN99887L7e+edd9S9e3ebZb/88ovKlSvnMD47O1snTpyQ0Wi8+oMBAAAAXIDhXAAAAIB/qePHjys2NlY//fSTypYta7feYDCoQYMGatCggaKiovT6669r27ZtatKkSaH2f/78eUVFRWn79u3avn27wxiTyaRSpUpp9uzZ+uSTT5SWlqZ77rlHMTExCg0NdbhNdna2srOzrfPp6emSJLPZLLPZXKjcrhWL2VKs7eEGYZZkyf/XIs4R/KO436OAG43ZbJbFYuF3BW6jsOciRXQAAADgX6pSpUqaNWtWoWK9vLw0cuTIQu/76NGj6tq1q9LT07V161bVq1fPYVxaWpoqVqyooKAgbdu2TWazWS+//LKaN2+un376SaVLl7bbJiYmRuPGjbNbnpqaqqysrELneE2cK97mcIOwSMr4388GVyYCd5NyNsXVKQBuzWw2Ky0tTRaLRR4eDJAB18vIyLh8kCiiAwAAAP8JK1eu1IQJE9SjRw899dRTTodfKYy4uDi1a9dOTz75pF599VX5+vo6jW3UqJGOHj1qs2zKlCmaO3eutmzZorZt29ptM2LECEVFRVnn09PTFRISooCAgKvK+4pkFm9zuEEUdFqrIAZJhY3AMoGuTgFwa2azWQaDQQEBARTR4RZKlChRqDiK6AAAAMC/2L59+zRw4EClpqZq2LBhatasmapWraqePXtqwoQJRS5KHz16VO3bt9c777yjLl26FGobs9ls80W54DZug8FxF15fX1+HhXkPD49i/8Jt8KCbMexZZMnvge7BOQJbFAWByzMYDC75TAccKex5yNkKAAAA/It9+OGH6tmzp3799Vc9+eSTqlatmuLj45WRkaH69etrz549Rdpf//79NWDAgEIX0Lds2aJatWrphx9+kCRlZWXphRdekNFo1AMPPFDUwwEAAACKHT3RAQAAgH+hPn36yGAwyGKxaOvWrdq6davN+gYNGqhFixZq27atvv76azVo0KBQ+127dq3i4uL0/vvv260zmUwymUy6++67NXXqVHXp0kXNmjXTyy+/rOeee07Hjx9Xdna2mjVrpg0bNlxyGBgAAADAXRS5iH7o0CHVqFHjkjFTpkyxGcMQAAAAQPHq0KGD03VZWVnq27evzp49q3Pnzqlbt2768ccf5ePjc9n9WiyWS643Go0ymUw2y3r37q3evXsXKm8AAADA3RR5OJfIyEhJ0tdff60NGzbYTUlJSZo9e/Y1TxQAAABA4XXu3FmdO3fW+fPntWrVKqWlpenOO+9U586d1alTJ9WqVUuS9MwzzygkJETTpk1zbcIAAACAm7riMdEff/xxLV68WE888YT132nTpmnjxo3XMj8AAAAAV6FZs2a69957tXnzZjVo0ECPPvqoEhMTbcZCHzNmjGbMmCGz2ezCTAEAAAD3VOgi+sW3bYaGhmrevHmqUqWK9d/o6OhrniAAAACAojt27Jh27typ4OBgPfPMMypfvrwSEhLUokULpaamatKkSdbYJk2aaO7cufLwuOI+NgAAAMC/VqGvkidOnCh/f3/9+eef6tevn7WobjAYrltyAAAAAK7MwoUL9c4771jn16xZo7Jly6pVq1Z6+umnddttt9nEt2nTprhTBAAAAG4IhX6w6KhRo/TCCy+oUaNGatWqleLi4mzWU0wHAAAA3Mdjjz2m8PBwdezYURaLRSkpKerYsaPi4uLk5+end999V++++67NNitWrHBRtgAAAID7KnQRXZLKlSsnHx8fde7cWU888YSCgoJ04sQJVa5cWSdPntQjjzyit95663rlCgAAAKCQwsLCFBgYqB49eshoNCouLk5Vq1bVrl27VL9+ffXs2VN+fn6uThMAAABwe1c86OG5c+eUlJSknJwcJScnKycnRydOnFCvXr2uZX4AAAAArtCdd96pkiVLqmnTpvL19dWMGTN09OhRNW/eXAMGDNDmzZvVuHFj3Xvvvbr33ntdnS4AAADglorUE12SgoKCrD/fdtttysjIsFlvsViUmJio6tWr6/Dhw1efIQAAAIArMnHiRIWGhkqSJk+eLEny9fXVs88+q86dO+ujjz7SmTNn5OPj48o0AQAAALdW6CL6ihUrtHXrVkVHR8tsNsvDw0Pbt2+3PmAUAAAAgHsJCQlRUlKSQkJC1LlzZ5t1/v7+ioqKslmWnp6u3NxcVahQoTjTBAAAANxaoYvodevW1cGDBzVlyhT16dNHNWvWvOw2mzZtuqrkAAAAAFy59PR0PfHEE4qIiNBzzz2n8uXLO4w7f/68PvnkE82ePVvz58+niA4AAABcoNBF9Bo1aigqKkpRUVE6ffq0PvroI02fPl2BgYGaOHEiF9oAAACAm6lQoYLWr1+vsWPHqmbNmmrcuLHuvPNOVapUSR4eHjp58qT27t2r7777Tu3atdPy5csVGBjo6rQBAAAAt3JFDxa96aab9OKLL+qPP/7QY489po8++kiNGjVSeHi4zVRYR48elZ+fn3r37m1dlp2drejoaIWFhSkoKEgdO3ZUYmKizXaJiYnq2rWrqlatquDgYA0ePFjZ2dk2MTt27FCzZs0UGhqqmjVr6r333rNrf/78+apXr56MRqMaN26srVu3Fu0FAQAAANxUiRIlNGnSJO3fv1+PPfaYEhMTtXr1ai1fvlwHDhzQ/fffr127dmnevHkU0AEAAAAHivxgUZuNvbw0ZMiQq0rAbDarZ8+eqlKlis3yAQMG6ODBg9q9e7fKlCmjYcOGqV27dtq7d688PT2Vk5OjVq1aqV27dlq0aJEyMzPVsWNHDR48WLNmzZIkxcfHq3Xr1po3b546d+6sffv2qXnz5vLz89Njjz0mSVq4cKFGjBihTZs2qXbt2lq6dKkiIiK0d+9eVa9e/aqODQAAAHAX/v7+6tWrl3r16uXqVAAAAIAbSpF7ov/yyy+aOnXqJWM2bNigF198sVD7e+2111SuXDl16tTJuiwhIUHz58/XlClT5OfnJy8vL02aNElJSUlavXq1JGnJkiU6fvy4YmJi5OXlJT8/P02dOlVz587ViRMnJEmTJ0/W/fffb32IUp06dfTSSy9p0qRJ1rbGjRunoUOHqnbt2pKkLl266L777tPMmTML/ZoAAAAAAAAAAP6dilxET0pK0pw5czR79mx9/fXXOnr0qM36hIQE9erVS7fffvtl97Vr1y5Nnz7d2nO8wDfffKNKlSrZDAnj4+Oj1q1ba+3atZLyH1rapk0b+fj4WGPCw8Pl7++v2NhYa0xkZKTNviMjI7V3714dP35cCQkJOnTokMOYgnYAAAAAAAAAAP9dVzSci5eXlxITE/Xdd99pz549ysvLU9euXdW0aVMNGDBAr7zyymVvE83MzFSPHj00bdo0hYaG2qxLSkpSUFCQ3TbBwcH6/fffrTH16tVzGFMwdrqj/QQHB0vKH089JydHkhzGXDz++oWys7Ntxl5PT0+XlD80jdlsdrrddWNxQZtwbxbLP5M4P2DLJe9TbopXAhczS+KdE8644v2T92wAAADA9QpdRLdYLDIYDJKk6tWra8KECdZ133//vTp16qSYmBg1adJEzz333GX3N2jQIN1xxx16/PHH7dZ5e3vLw8O+k3xB+1cTc/F6SZeMcSQmJkbjxo2zW56amqqsrKxLbns9GDLOFHubcHcWGc5lSAbpf/8BrFJSsi8f9B+R5enp6hTgZiyS0v93XcC7Jy52JiWl2NvMyMgo9jYBAAAA2Cp0Ef32229XWlqaypUrp7y8PK1du1aJiYlav369EhMT9cYbb6h58+bq1auXWrdurVWrVqlUqVIO97V06VJ9/fXX+uWXXxyuNxqNSkpKsluenJxs7Ul+pTHJycmS/umRLuX3WA8LC3O4D0dGjBihqKgo63x6erpCQkIUEBCgcuXKOd3uerFkphV7m3BzFotkkSxlKkiX+aMQ/nsCA8u7OgW3cSYvz9UpwM0U9PmtkJdX9DHv8K9XOjCw2NssUaLEVW2fkJBQqDiDwaCbb77Z2skEAAAAwD8KXUTfs2ePDh8+rN27d2vLli3q27ev/vrrL/Xr108zZsxQ5cqVJUmxsbHq1KmTnnrqKX366acO97V69WolJiaqQoUKdus++ugjLVmyRCkpKfr555/VoEEDSVJeXp42b95sHT+9bdu26tu3r3Jzc+XllX8Y8fHxSklJUYsWLawxa9as0TPPPGPd/8aNG9WwYUNVqlRJktSwYUOtWbNGzz//vE1Mu3btnL4Wvr6+8vX1tVvu4eHhsHf8dWfgaz4uZs4vnhsMnB+w45L3KTfFKwFHDMo/Nzg/cDFXvH9ebZu33nqrAgICZDAYdOLECVWpUkUWi8Uu7syZMwoICFBcXNxVtQcAAAD8GxX6qjwlJUVlypRRt27ddP78eZlMJkVHR6t8+fIyGAzWwrWHh4emT5+uChUqWMccv9j8+fNlsVhspjFjxqhXr16yWCzq0qWL+vTpo6ioKKWnpysvL08jR46Un5+f2rdvL0mKiIhQYGCgRo0apby8PKWlpWnQoEHq06ePKlasKEkaOHCgYmNjtWLFCknSgQMHNHHiRA0fPtyay7Bhw/TGG2/owIEDkqTly5dr7dq1GjBgwBW8nAAAAID7qFWrlmbOnKl33nlHderU0b59+7R//367ac+ePTp+/Lir0wUAAADcUqF7om/btk39+vVT3759tXv3bu3fv1/x8fH6/PPPJUm7du3S4MGD1b9/f40bN05t27aVj4/PFSc2Y8YMRUdHq06dOsrLy9Odd96pdevWWXude3l5ad26dRo4cKBCQkLk4eGhLl26aNKkSdZ9hIWFadWqVYqKilL//v1VqlQpjR49Wt26dbPGdO/eXenp6erQoYMyMzNlNBq1atUqm+FdAAAAgBuZwWCwPvdn165dds/xqVq1qlq1auWK1AAAAAC3V+gielhYmFasWKE//vhDy5Yt06uvvqonn3xSf//9t/z8/BQcHKyGDRuqU6dOOnv2rBYsWFCkRMaOHWsz7+vrq6lTp2rq1KlOtzEajVq+fPkl99usWTP98MMPl4x57rnnCvUwVAAAAOBGcmHxvMATTzyh8PBwbdy4Ua1atdKGDRt08uRJzZs3z0VZAgAAAO6t0EX0IUOGyGAwyGKxKCkpSZs3b1ZKSorGjx+vu+++WyVKlFCvXr30008/6eOPP9YPP/ygu++++3rmDgAAAOASfvnlF/Xq1UtS/rjnUn5nlcWLF6tx48ZavHixbr/9dlemCAAAALi9QhfRN2zYoK+++kqJiYk6ceKEGjZsqA4dOui+++7T119/rffee0/t27fXkSNHtGLFCr300kvasmXL9cwdAAAAwCWcP3/ebllBz/SLe6gDAAAAcKzQDxbdsGGD3nzzTQUGBspgMGjOnDl67bXXdOLECXXv3l1169bVkCFDNHDgQN199906e/asjh07dj1zBwAAAHAJQUFB1ik4ONhhDMV0AAAA4NIK3RO9TZs2atOmjSTpzz//lI+PjyZOnKjExETVrl1bHTt2VIsWLdSiRQtJ0rx58xQSEnJ9sgYAAABwWZUqVdLYsWPl6emp8ePHS5LOnj2r5s2bKz4+Xs2bN9cff/yh5s2ba+3atfL19XVxxgAAAID7KXQR/ULDhw+XJLVr1866bNy4cTYxDRo0uIq0AAAAAFwLFz9cdO3atcrJybGL8/HxKc60AAAAgBtGkYroCxYsKFScwWDQ7bffrnr16l1RUgAAAACunqOhWmrWrOmCTAAAAIAbV5GK6EOHDlXt2rVVqlQp7d27V5GRkQ7jTp8+rYkTJ+r333+/JkkCAAAAKLrffvtNzz77rCTp77//VvXq1R3GWSwWGQwGHT58uDjTAwAAAG4IRSqiV6lSRb169VJwcLDGjRun999/X2lpafLx8dHChQutF+iJiYl68MEHr0vCAAAAAAonISHB1SkAAAAAN7wrGhP9QnPnzlXdunU1e/ZsPfvss9q4caNmzJihb7/99lrkBwAAAOAKVapUydUpAAAAADc8j6IE5+Tk6OTJk0pJSVFubq6efPJJLV++3Lo+Ly9PAwYM0NNPP63KlStf82QBAAAAFM3OnTutP999992SpBdeeMFV6QAAAAA3nCIV0cuVK6cVK1bovffeU2hoqF5//XX5+/srMTFR2dnZmjNnjpo0aaKHH374OqULAAAAoCj69Olj/fnUqVPKycnRmjVrlJGRofT0dKWnpysjI8OFGQIAAADurUjDuXz00UeS8h885O3trcqVK+vee+/V1KlTlZCQoOHDh+uxxx7T+fPn5e3tfV0SBgAAAFB4FovF+vPJkydlNBp16tQp1a1b17ouJydHRqNRcXFxrkoTAAAAcFtFKqLffvvtCgoKkq+vr86cOaPhw4dr0qRJ+vjjjzVixAh9//33eumll9SjRw8tXbr0euUMAAAA4DK+++47SdK5c+e0ZcsWmc1mlS9fXocPH1bNmjX1xx9/WGPNZrPKlCnjqlQBAAAAt1akIvott9yi/v37Kzg4WGPHjlWdOnU0YMAA6/qSJUvq7bff1n333ad169apbdu21zxhAAAAAJf35ptvSsrvff7mm2/KYrEoOzvbLm7Tpk2qX7++atSoUdwpAgAAADeEIhXRL2QwGNSkSROdOnVKN998s/VWUIPBoLFjx6pChQrXLEkAAAAARbNy5UpJUu3atbVixQpJ+Z1iChw7dkzPPPOMcnNz9eWXX+qXX35xSZ4AAACAu7uiIrrBYLD+3KFDB0nSnj17rMtatGhxlWkBAAAAuJ5iY2MVGRmpQYMGuToVAAAAwK0VqYgeFxenfv36yWAwKDc3V6VKlXIYZ7FYZDAYdPbs2WuSJAAAAIArUzCsiyTdddddkvI7wvTu3dtFGQEAAAA3liIV0c1m8/XKAwAAAMA19sorr2jDhg0aP368pPzOLnfeeaf1X0mqVq2aPvvsM1emCQAAALi1Kx4THQAAAIB7O3TokIYMGaK7775bkrRjxw4dOHBATz75pKT8onr79u1dmSIAAADg9q6oiN6lSxd5enqqRIkSKlmypEqXLq2yZcuqQoUKuvnmmxUWFqbbb7/9WucKAAAAoIgqV66sKlWqSJKOHj2qkydPWucBAAAAXN4VFdE3b96suXPnKjs7W1lZWTp37pwyMjKUnJys3bt3a+fOnapatarWrVt3rfMFAAAAUAT9+/dX+fLlJUlpaWk6d+6cPv74Y0n5PdGPHDmizp07a9myZa5MEwAAAHBbV1RE9/b21kMPPWSz7Pz58/L29pYknTlzRhUqVLj67AAAAABcsbfffltnzpy5bJyXF6M8AgAAAM5c0dWywWCw/pyWlqYXXnhBmZmZ+vzzzyXlF9F9fHyuTYYAAAAArkjFihVVsWLFS8YkJyercuXKxZQRAAAAcOPxKGzg8ePHVbFiRXXt2lXnz5+XlD+m4j333KMaNWpo0aJF1tjc3Fy9/vrr1z5bAAAAAIXWvHnzy8a0aNGiGDIBAAAAblyF7oleqVIlxcXFafXq1Tp06JAaNGigadOmaeHChQoPD7eJDQoK0oABA655sgAAAAAKLzU1VVJ+b/Njx47ZrCtZsqTq168vi8XiitQAAACAG0aRhnOpUqWKBgwYoAEDBmjFihUaMmSItmzZcr1yAwAAAHCVPv74Y23cuFF79uxRYmKibr75ZpUsWVJ5eXn68ccfbYZqBAAAAGCvyGOi79u3T926dZMkWSwWNWnSxLrOYrHIw8NDP/3007XLEAAAAMAV++KLL1SuXDlFR0dr9uzZ6t69u2699Vb169dPo0ePVkpKiqKioiRJU6ZMcXG2AAAAgPsp9JjoZ86cUVJSks6dO6ewsDCtXLlSK1eu1NmzZ60/r1y5Uunp6dczXwAAAACF0KdPHx0/flyff/65atasabfew8NDpUuXlre3t2rUqKEaNWoUet8ffvih6tWrp+DgYNWqVUvvvvvuJeMTExPVtWtXVa1aVcHBwRo8eLCys7OLfEwAAACAKxS6J3p8fLzatGmjSpUqKTw8XFWqVJEk+fj4WH+WJG9v72ufJQAAAIAieeqpp5STk6Pw8HCFhYWpatWqNuv9/f01fPhwzZ8/XwMHDiz0fhcuXKgxY8Zo3bp1qlu3ruLj49W8eXOVK1dOjz/+uF18Tk6OWrVqpXbt2mnRokXKzMxUx44dNXjwYM2aNetqDxMAAAC47grdEz08PFwnTpzQ+PHjlZ2drdOnT+vkyZPKy8vT6dOnderUKZ06dUpms1mnT5+W2Wy+nnkDAAAAuIRmzZrp1KlTeuedd1SxYsVrtt8dO3bojTfeUN26dSVJtWrVUvfu3bVs2TKH8UuWLNHx48cVExMjLy8v+fn5aerUqZo7d65OnDhxzfICAAAArpcij4l+6623ateuXQoPD5fFYpEkNWrUyCYmPDxcK1eutF5YAwAAACh+iYmJuuWWW+Tv72+3LjMzUytWrFBWVpbOnj2rUqVKFWqf77zzjt2yX375RUFBQQ7jN23apDZt2sjHx8e6LDw8XP7+/oqNjVXXrl0LeTQAAACAaxS5iF6vXj39+eef1yMXAAAAANeQxWLRHXfcoVtuuUV16tSxWZeWlqaFCxcqJSVF9erV04oVK1SvXr0i7f/8+fOKiorS9u3btX37docxSUlJDvcbHBysxMREh9tkZ2fbjJle8Nwls9lc7He8WsyWYm0PNwizJEv+vxZxjuAf3JUPXJrZbJbFYuF3BW6jsOdikYvoAAAAANzfunXrdP78ef3xxx9644039NVXX+nYsWNav369du/erYoVK2rp0qWqXbu25s2bpxdeeEGxsbGF3v/Ro0fVtWtXpaena+vWrU4L8N7e3vLwsB9F0mAwON13TEyMxo0bZ7c8NTVVWVlZhc7xmjhXvM3hBmGRlPG/n52fyvgPSjmb4uoUALdmNpuVlpYmi8Xi8PoAKG4ZGRmXD1IRi+gzZszQ33//Xej40aNHF2X3AAAAAK6RDRs2KCEhQZ9//rlatGghLy8vhYeHW9c3adJEUn4x++6771ZWVpZOnz6tm2666bL7jouLU7t27fTkk0/q1Vdfla+vr9NYo9GopKQku+XJyckKDg52uM2IESMUFRVlnU9PT1dISIgCAgJUrly5y+Z3TWUWb3O4QRR0WqugIjxpDP8FgWUCXZ0C4NbMZrMMBoMCAgIoosMtlChRolBxRSqiT506Vd26dbPOv/fee3r22WclSbNnz1a/fv0kSe+++64GDBhQlF0DAAAAuIamTJmi6OhoDRo0SKVLl9a8efMcxhU852jBggWFKqAfPXpU7du31zvvvKMuXbpcNr5t27bq27evcnNz5eWV//UjPj5eKSkpatGihcNtfH19HRbmPTw8iv0Lt8GDbsawZ5Elvwe6B+cIbFEUBC7PYDC45DMdcKSw56HBUnDVXAjVqlWzGQ+9Zs2a+uOPP+zWhYSE6NixY0XJ94aWnp6u8uXLKy0trfh7xkhaeiit2NuEm7OYZcg4JUvZCpKBDyXY6lKjvKtTcBtnFi92dQpwM2ZJJz095Z+XR8dC2CndvXuxt3mtrjNPnTqlChUqOFz31Vdf6eGHHy70vtq3b6+77rpLY8aMKVR8bm6uGjZsqMjISE2cOFGZmZnq3LmzwsLCNHv27ELtw5XX2ysyVxRre7gxWMwW6aQkf4rosNWxTEdXpwC4NbPZrJSUFAUGBlJEh1so7HVmkc7Wi8ctvHDe2c8AAAAAXKuggH748GG7dUUpoEvS2rVrNWvWLBmNRrtJkkwmk4xGo5YuXSpJ8vLy0rp167Rv3z6FhISobt26ql+/vqZPn351BwUAAAAUk6t6sOiFndiL0KEdAAAAgAvcfffdSkm5uofeXe6632g0ymQy2S1bvnz5VbULAAAAuAr3TQAAAAD/EXR8AQAAAIruqnqiAwAAAHBPCxYssFuWk5PjcHmBHj16WB/+CQAAACDfVV0hMw46AAAA4J42b95stywnJ8fhcin/ev6xxx6jiA4AAABcpEhXyBaLRePHj7fOnzx5UuPHj5fFYtHp06et69LS0jRhwgSNGjXq2mYLAAAAoFDmzZtnt2z16tUOlwMAAABwrkhF9P79++v06dPW+WeffVbnzp2TJPXr18/684ABA3T27NlrmCYAAACAq8WY6AAAAEDRFamIPmzYsOuVBwAAAIDr5L777tN3332nhQsXWpf99NNPOnPmjE1ckyZNijs1AAAAwO0x4CEAAADwL1W/fn398ssvOnbsmL777jt9++23iouL0913363BgwfLaDTKYrHo+++/V9OmTbVmzRpXpwwAAAC4HYroAAAAwL/UhT3Nc3NzlZWVJW9vb+Xm5kqStWheu3ZtCugAAACAEx6uTgAAAACAaxkMBlenAAAAALgteqIDAAAA/1KXepCoxWLRe++9J4vFotOnT+v9999Xnz595OXFVwQAAADgQlwhAwAAAP9SixYtsv7cqFEjhYSEyNPTU35+furZs6f2798vSerWrZv279+vvLw8iugAAADARbhCBgAAAP6lmjRpIknaunWr/Pz85OfnZ103bNgwF2UFAAAA3FgYEx0AAAD4lwsODnZ1CgAAAMANiyI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAEy4roqelpalfv34KDQ1VaGiowsPD9cUXX1jXZ2dnKzo6WmFhYQoKClLHjh2VmJhos4/ExER17dpVVatWVXBwsAYPHqzs7GybmB07dqhZs2YKDQ1VzZo19d5779nlMn/+fNWrV09Go1GNGzfW1q1br89BAwAAAAAAAABuKC4ronfp0kU5OTnat2+fEhIS9Oabb6pnz57auXOnJGnAgAHavn27du/erYSEBIWFhaldu3bKy8uTJOXk5KhVq1YyGo06ePCgfvvtN8XFxWnw4MHWNuLj49W6dWu9+OKLSkhI0PLlyzV69GgtWbLEGrNw4UKNGDFCS5culclk0rBhwxQREaHDhw8X7wsCAAAAAAAAAHA7LiuiL1q0SLNnz1aZMmUkSc2bN1dYWJi2bdumhIQEzZ8/X1OmTJGfn5+8vLw0adIkJSUlafXq1ZKkJUuW6Pjx44qJiZGXl5f8/Pw0depUzZ07VydOnJAkTZ48Wffff786d+4sSapTp45eeuklTZo0yZrHuHHjNHToUNWuXVtSfnH/vvvu08yZM4vz5QAAAAAAAAAAuCGXFdEDAgLk4+MjScrKytKcOXMUHx+vpk2b6ptvvlGlSpUUHh5ujffx8VHr1q21du1aSdKmTZvUpk0b6z4kKTw8XP7+/oqNjbXGREZG2rQbGRmpvXv36vjx40pISNChQ4ccxhS0AwAAAAAAAAD47/JydQJGo1FJSUlq0KCBli1bpsaNGys2NlZBQUF2scHBwfr9998lSUlJSapXr57DmIKx05OSkuz2ExwcLCl/PPWcnBxJchhz8fjrF8rOzrYZez09PV2SZDabZTabL3vM15zFBW3CvVks/0zi/IAtl7xPuSleCVzMLIl3TjjjivdP3rMBAAAA13N5Ed1kMun06dOaMmWK5s6dqwceeEDe3t7y8LDvJG8wGKw/X2nMxeslXTLGkZiYGI0bN85ueWpqqrKysi657fVgyDhT7G3C3VlkOJchGaT//QewSknJvnzQf0SWp6erU4CbsUhK/991Ae+euNiZlJRibzMjI6PY2wQAAABgy+VFdEm66aabNGHCBDVp0kQzZ85UtWrVlJSUZBeXnJxs7Ule0IO9qDHJycmS/umRLuX3WA8LC3O4D0dGjBihqKgo63x6erpCQkIUEBCgcuXKFeaQrylLZlqxtwk3Z7FIFslSpoJ0mT8K4b8nMLC8q1NwG2f+97BqoEBBn98KeXmuG/MObqt0YGCxt1miRIlibxMAAACALZcU0c1ms9asWaMOHTrYLK9YsaL++usvPf3000pJSdHPP/+sBg0aSJLy8vK0efNmzZo1S5LUtm1b9e3bV7m5ufLyyj+M+Ph4paSkqEWLFtaYNWvW6JlnnrG2sXHjRjVs2FCVKlWSJDVs2FBr1qzR888/bxPTrl07p/n7+vrK19fXbrmHh4fD3vHXnYGv+biYOb94bjBwfsCOS96n3BSvBBwxKP/c4PzAxVzx/sl7NgAAAOB6LrkqT01N1dNPP61x48ZZxxZfv3691q9fr4iICAUEBKhPnz6KiopSenq68vLyNHLkSPn5+al9+/aSpIiICAUGBmrUqFHKy8tTWlqaBg0apD59+qhixYqSpIEDByo2NlYrVqyQJB04cEATJ07U8OHDrbkMGzZMb7zxhg4cOCBJWr58udauXasBAwYU50sCAAAAAAAAAHBDLumJXqlSJe3YsUPR0dGqXr26LBaLKlWqpPnz56tVq1aSpBkzZig6Olp16tRRXl6e7rzzTq1bt87a69zLy0vr1q3TwIEDFRISIg8PD3Xp0kWTJk2ythMWFqZVq1YpKipK/fv3V6lSpTR69Gh169bNGtO9e3elp6erQ4cOyszMlNFo1KpVq2yGdwEAAAAAAAAA/DcZLBaLxdVJ3OjS09NVvnx5paWluWRM9KWHGBMdF7GYZcg4JUvZCgznAjtdajAmeoEzixe7OgW4GbOkk56e8mdMdDhQunv3Ym/T1deZ7sKVr8OKzBXF2h5uDBazRTopyV8yePAMIvyjY5mOrk4BcGtms1kpKSkKDAxk2Dq4hcJeZ3K2AgAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAAAAAAAAOEERHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAAAAAACcoIgOAAAAAAAAAIATFNEBAAAAAAAAAHCCIjoAAAAAAAAAAE5QRAcAAAAAAAAAwAmK6AAAAAAAAAAAOEERHQAAAAAAAAAAJyiiAwAAAAAAAADgBEV0AAAAAEViNpu1Y8cORUVFqUKFCpo/f/4l4yMjI+Xv7y+j0WidmjVrVjzJAgAAAFfJy9UJAAAAALixzJs3T3PmzFHr1q3l6el52XiTyaTFixerdevWxZAdAAAAcG3REx0AAABAkTz99NPatWuXJk6cqNKlS1823mQyKSQkpBgyAwAAAK49iugAAAAArpvs7GydOHFCRqPR1akAAAAAV4ThXAAAAABcNyaTSaVKldLs2bP1ySefKC0tTffcc49iYmIUGhrqcJvs7GxlZ2db59PT0yXlj8VuNpuLJe8CFrOlWNvDDcIsyZL/r0WcI/hHcb9HATcas9ksi8XC7wrcRmHPRYroAAAAAK6btLQ0VaxYUUFBQdq2bZvMZrNefvllNW/eXD/99JPD4WBiYmI0btw4u+WpqanKysoqjrT/ca54m8MNwiIp438/G1yZCNxNytkUV6cAuDWz2ay0tDRZLBZ5eDBABlwvIyPj8kGiiA4AAADgOmrUqJGOHj1qs2zKlCmaO3eutmzZorZt29ptM2LECEVFRVnn09PTFRISooCAAJUrV+6652wjs3ibww2ioNNaBTFIKmwElgl0dQqAWzObzTIYDAoICKCIDrdQokSJQsVRRAcAAABwXZnNZpsvygW3cRsMjrvw+vr6ytfX1265h4dHsX/hNnjQzRj2LLLk90D34ByBLYqCwOUZDAaXfKYDjhT2PORsBQAAAHDdbNmyRbVq1dIPP/wgScrKytILL7wgo9GoBx54wLXJAQAAAIVAER0AAADANWMymWQ0GrV06VJJUrNmzfTyyy/rueeeU3BwsIxGo5KSkrRhwwaHvc0BAAAAd8NwLgAAAACu2JEjR2zmjUajTCaTzbLevXurd+/exZcUAAAAcA3REx0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIDgAAAAAAAACAExTRAQAAAAAAAABwgiI6AAAAAAAAAABOUEQHAAAAAAAAAMAJiugAAAAAAAAAADhBER0AAAAAAAAAACcoogMAAAAAAAAA4ARFdAAAAAAAAAAAnKCIjv9v7+6jo6wOPI7/ZjJJkNAYCQmamSDY2MNLENosHFpLSkFeQginW40QXV0osGhgkWQxGi24sewmsjlEXEQLh5KWuraJnmNsIFEI4oEKgiytLh5IgYWYlxLAJZMAmZDM7B/IlGHmgQSTmYl8P+eMZO5zn+feZxye3Plx5z4AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMBAQEP0X/3qV0pMTJTVatXQoUP12muveWx3OBx69tlnlZCQoLi4OM2cOVN1dXUederq6jRr1iwNHjxYVqtVWVlZcjgcHnX27t2r8ePHa9CgQbr33nu1fv16r74UFxcrMTFRNptNY8aM0e7du7v/hAEAAAAAAAAAvUrAQvTNmzfrhRde0O9//3vV1dXpnXfe0S9+8Qu98cYb7jqZmZnas2ePPvnkE9XU1CghIUEpKSnq6OiQJLW1tWny5Mmy2Ww6evSoDh06pAMHDigrK8t9jMOHD2vKlClaunSpampqVFZWphUrVqikpMSjL7m5uSotLVVtba1ycnKUmpqq48eP++8FAQAAAAAAAAAEnYCF6Hv37tWqVas0YsQISdLQoUOVkZGht99+W5JUU1Oj4uJirV69WlFRUbJYLCooKFB9fb22bNkiSSopKdGpU6eUn58vi8WiqKgoFRUVaePGjTpz5owkqbCwUD/60Y/04IMPSpKGDx+up59+WgUFBe6+5OXladmyZRo2bJgkKT09XcnJyVq7dq3fXg8AAAAAAAAAQPAJWIj+6quvKiMjw6Pss88+U2RkpCRp586dGjhwoJKSktzbw8LCNGXKFFVUVEiSduzYoalTpyosLMxdJykpSdHR0aqqqnLXSUtL82gnLS1NBw8e1KlTp1RTU6Njx475rHOlHQAAAAAAAADArckS6A5I0qVLl5Sdna09e/Zoz549kqT6+nrFxcV51bVarTpy5Ii7TmJios86V9ZO93Ucq9Uq6fJ66m1tbZLks861669f4XA4PNZdt9vtkiSn0ymn03njE+5urgC0ieDmcv3tId4f8BSQ61SQ4pXAtZySuHLCSCCun1yzAQAAgMALeIh+8uRJzZo1S3a7Xbt373aH4qGhoTKbvSfKm0wm9883W+fa7ZKuW+da+fn5ysvL8yo/ffq0WltbDffrKabm835vE8HOJdPFZskkffUfwK2x0XHjSreI1pCQQHcBQcYlyf7VmICrJ651vrHR7202Nzf7vU0AAAAAngIaoh84cEApKSl6/PHH9W//9m8KDw93b7PZbKqvr/fap6GhwT2T/GbrNDQ0SPrbjHTp8oz1hIQEn8e4Vm5urrKzs93P7Xa74uPjFRMT416Oxp9cLU1+bxNBzuWSXJKrX3/pOv8ghFtTbOztge5C0Dj/1Y2qgSuuzPnt39ERuDXvELQiYmP93mafPn383iYAAAAATwEL0U+ePKnp06fr1VdfVXp6utf2iRMnqrGxUZ9++qnuu+8+SVJHR4c++OADrVu3TpI0bdo0LViwQO3t7bJYLp/K4cOH1djYqEmTJrnrbN26VfPnz3cfe9u2bRo9erQGDhwoSRo9erS2bt2qJUuWeNRJSUnx2ffw8HCPwP8Ks9nsc2Z8jzPxMR/Xcl4Oz00m3h/wEpDrVJDilYAvJl1+b/D+wLUCcf3kmg0AAAAEXsBG5U8++aQyMzN9BuiSFBMTo7lz5yo7O1t2u10dHR16/vnnFRUVpenTp0uSUlNTFRsbq+XLl6ujo0NNTU1avHix5s6dqwEDBkiSFi1apKqqKr377ruSpOrqaq1cuVLPPPOMu62cnBytWrVK1dXVkqSysjJVVFQoMzOzJ18CAAAAAAAAAECQC9hM9IqKCh04cEAbNmzw2lZbWytJeuWVV/Tss89q+PDh6ujo0NixY1VZWemedW6xWFRZWalFixYpPj5eZrNZ6enpKigocB8rISFB5eXlys7O1pNPPqm+fftqxYoVmj17trtORkaG7Ha7ZsyYoZaWFtlsNpWXl3ss7wIAAAAAAAAAuPUELER3uVw3rBMeHq6ioiIVFRUZ1rHZbCorK7vuccaPH6/9+/dft87ChQu1cOHCG/YJAAAAAAAAAHDrYJFFAAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAABd4nQ6tXfvXmVnZ6t///4qLi6+bv26ujrNmjVLgwcPltVqVVZWlhwOh386CwAAAHxNhOgAAAAAumTTpk1asmSJ+vbtq5CQkOvWbWtr0+TJk2Wz2XT06FEdOnRIBw4cUFZWlp96CwAAAHw9hOgAAAAAumTevHnat2+fVq5cqYiIiOvWLSkp0alTp5Sfny+LxaKoqCgVFRVp48aNOnPmjJ96DAAAANw8QnQAAAAAPWbHjh2aOnWqwsLC3GVJSUmKjo5WVVVVAHsGAAAAdI4l0B0AAAAA8M1VX1+vxMREr3Kr1aq6ujqf+zgcDo810+12u6TLa7E7nc6e6agBl9Pl1/bQSzgluS7/6RLvEfyNv69RQG/jdDrlcrn4u4Kg0dn3IiE6AAAAgB4TGhoqs9n7C7Amk8lwn/z8fOXl5XmVnz59Wq2trd3avxu66N/m0Eu4JDV/9bPxWxm3oMYLjYHuAhDUnE6nmpqa5HK5fI4PAH9rbm6+cSURogMAAADoQTabTfX19V7lDQ0NslqtPvfJzc1Vdna2+7ndbld8fLxiYmIUGRnZY331qcW/zaGXuDJprb9YJBUeYvvFBroLQFBzOp0ymUyKiYkhREdQ6NOnT6fqEaIDAAAA6DHTpk3TggUL1N7eLovl8sePw4cPq7GxUZMmTfK5T3h4uMLDw73KzWaz3z9wm8xMM4Y3l1yXZ6CbeY/AE6EgcGMmkykgv9MBXzr7PuTdCgAAAKDHpKamKjY2VsuXL1dHR4eampq0ePFizZ07VwMGDAh09wAAAIAbIkQHAAAA0G1qa2tls9lUWloqSbJYLKqsrNTnn3+u+Ph4jRgxQiNHjtSaNWsC3FMAAACgc1jOBQAAAMBNO3HihMdzm82m2tpar7KysjI/9goAAADoPsxEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAAwEL0Z1Op/bu3avs7Gz1799fxcXFHtsdDoeeffZZJSQkKC4uTjNnzlRdXZ1Hnbq6Os2aNUuDBw+W1WpVVlaWHA6HR529e/dq/PjxGjRokO69916tX7/eqy/FxcVKTEyUzWbTmDFjtHv37m4/XwAAAAAAAABA7xOwEH3Tpk1asmSJ+vbtq5CQEK/tmZmZ2rNnjz755BPV1NQoISFBKSkp6ujokCS1tbVp8uTJstlsOnr0qA4dOqQDBw4oKyvLfYzDhw9rypQpWrp0qWpqalRWVqYVK1aopKTEXWfz5s3Kzc1VaWmpamtrlZOTo9TUVB0/frznXwQAAAAAAAAAQFALWIg+b9487du3TytXrlRERITHtpqaGhUXF2v16tWKioqSxWJRQUGB6uvrtWXLFklSSUmJTp06pfz8fFksFkVFRamoqEgbN27UmTNnJEmFhYX60Y9+pAcffFCSNHz4cD399NMqKChwt5WXl6dly5Zp2LBhkqT09HQlJydr7dq1/ngZAAAAAAAAAABBLCjXRN+5c6cGDhyopKQkd1lYWJimTJmiiooKSdKOHTs0depUhYWFueskJSUpOjpaVVVV7jppaWkex05LS9PBgwd16tQp1dTU6NixYz7rXGkHAAAAAAAAAHDrsgS6A77U19crLi7Oq9xqterIkSPuOomJiT7rXFk73ddxrFarpMvrqbe1tUmSzzrXrr9+NYfD4bH2ut1ul3R5nXen03nD8+t2rgC0ieDmcv3tId4f8BSQ61SQ4pXAtZySuHLCSCCun1yzAQAAgMALyhA9NDRUZrP3JHmTyfS161y7XdJ16/iSn5+vvLw8r/LTp0+rtbX1uvv2BFPzeb+3iWDnkulis2SSvvoP4NbY6LhxpVtEq497cuDW5pJk/2pcwNUT1zrf2Oj3Npubm/3eJgAAAABPQRmi22w21dfXe5U3NDS4Z5LfbJ2GhgZJf5uRLl2esZ6QkODzGL7k5uYqOzvb/dxutys+Pl4xMTGKjIzszCl2K1dLk9/bRJBzuSSX5OrXX7rBPwrh1hMbe3uguxA0zn91s2rgiitzfvt3dATnmncIqIjYWL+32adPH7+3CQAAAMBTUIboEydOVGNjoz799FPdd999kqSOjg598MEHWrdunSRp2rRpWrBggdrb22WxXD6Nw4cPq7GxUZMmTXLX2bp1q+bPn+8+9rZt2zR69GgNHDhQkjR69Ght3bpVS5Ys8aiTkpJi2L/w8HCFh4d7lZvNZp+z43uciY/5uJbzcnhuMvH+gJeAXKeCFK8EfDHp8nuD9weuFYjrJ9dsAAAAIPCCclQeExOjuXPnKjs7W3a7XR0dHXr++ecVFRWl6dOnS5JSU1MVGxur5cuXq6OjQ01NTVq8eLHmzp2rAQMGSJIWLVqkqqoqvfvuu5Kk6upqrVy5Us8884y7rZycHK1atUrV1dWSpLKyMlVUVCgzM9PPZw0AAAAAAAAACDZBORNdkl555RU9++yzGj58uDo6OjR27FhVVla6Z51bLBZVVlZq0aJFio+Pl9lsVnp6ugoKCtzHSEhIUHl5ubKzs/Xkk0+qb9++WrFihWbPnu2uk5GRIbvdrhkzZqilpUU2m03l5eUey7sAAAAAAAAAAG5NJpfL5Qp0J3o7u92u22+/XU1NTQFZE730GGui4xoup0zNX8r1rf4s5wIv6d9mTfQrzr/5ZqC7gCDjlHQ2JETRrIkOHyIyMvzeZqDHmddTXFyswsJCnTt3TnfddZeKior0wx/+0GfdtLQ0ffTRR7rtttvcZUOGDNGuXbs61VYgX4d3W971a3voHVxOl3RWUrRkMnMPIvzNzH4zA90FIKg5nU41NjYqNjaWZesQFDo7zgzamegAAAAAgtPmzZuVm5urHTt2aNiwYSotLVVqaqoOHjyoe+65x6t+bW2t3nzzTU2ZMiUAvQUAAAC+Hv7JBwAAAECX5OXladmyZRo2bJgkKT09XcnJyVq7dq3P+rW1tYqPj/dnFwEAAIBuQ4gOAAAAoNNqamp07NgxpaWleZSnpaWpoqLCq77D4dCZM2dks9n81UUAAACgW7GcCwAAAIBOq6+vlyTFxcV5lFutVtXV1XnVr62tVd++ffX666/rv/7rv9TU1KTvf//7ys/P16BBg3y24XA45HA43M/tdruky+uoOp3O7jqVTnE5uYUUfHBKcl3+0yXeI/gbf1+jgN7G6XTK5XLxdwVBo7PvRUJ0AAAAAJ0WGhoqSV43AzOZfN9csampSQMGDFBcXJw++ugjOZ1OPffcc5o4caL+/Oc/KyIiwmuf/Px85eXleZWfPn1ara2t3XAWXXDRv82hl3BJav7qZ+4riqs0XmgMdBeAoOZ0OtXU1CSXy8WNRREUmpubb1xJhOgAAAAAuuDKsiz19fVKSEhwlzc0NMhqtXrV/973vqeTJ096lK1evVobN27Url27NG3aNK99cnNzlZ2d7X5ut9sVHx+vmJgYRUZGdtepdE6Lf5tDL3Fl0lp/sUgqPMT2iw10F4Cg5nQ6ZTKZFBMTQ4iOoNCnT59O1SNEBwAAANBpAwcO1OjRo7V161YtWbLEXb5t2zalpKT43MfpdHp8UL7yNW6j2evh4eEKDw/3KjebzX7/wG0yM80Y3lxyXZ6BbuY9Ak+EgsCNmUymgPxOB3zp7PuQdysAAACALsnJydGqVatUXV0tSSorK1NFRYUyMzO96u7atUtDhw7V/v37JUmtra166qmnZLPZNGHCBH92GwAAALgpzEQHAAAA0CUZGRmy2+2aMWOGWlpaZLPZVF5eroSEBNXW1mrcuHEqKipSenq6xo8fr+eee04LFy7UqVOn5HA4NH78eL3//vs+Z5sDAAAAwYYQHQAAAECXLVy4UAsXLvQqt9lsqq2t9SibM2eO5syZ46eeAQAAAN2L5VwAAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAACE6AAAAAAAAAAAGCNEBAAAAAAAAADBAiA4AAAAAAAAAgAFCdAAAAAAAAAAADBCiAwAAAAAAAABggBAdAAAAAAAAAAADhOgAAAAAAAAAABggRAcAAAAAAAAAwAAhOgAAAAAAAAAABgjRAQAAAAAAAAAwQIgOAAAAAAAAAIABQnQAAAAAAAAAAAwQogMAAAAAAAAAYIAQHQAAAAAAAAAAA4ToAAAAAAAAAAAYIEQHAAAAAAAA4KW4uFiJiYmy2WwaM2aMdu/ebVi3rq5Os2bN0uDBg2W1WpWVlSWHw+FR549//KN+8pOfaNCgQRoyZIieeuopnT9/3r3d6XRq7969ys7OVv/+/VVcXOzVzsWLF7VlyxbNnj1bYWFhOnHiRHedLmCIEB0AAAAAAACAh82bNys3N1elpaWqra1VTk6OUlNTdfz4ca+6bW1tmjx5smw2m44ePapDhw7pwIEDysrKctc5fPiw0tLSNH/+fNXU1OjgwYP67LPPtHTpUnedTZs2acmSJerbt69CQkJ89mvJkiX6z//8Tw0ePFiXLl3q9vMGfCFEBwAAAAAAAOAhLy9Py5Yt07BhwyRJ6enpSk5O1tq1a73qlpSU6NSpU8rPz5fFYlFUVJSKioq0ceNGnTlzRpK0b98+zZkzRzNmzJAkRUVFKSsrS2+//bb7OPPmzdO+ffu0cuVKRURE+OzXhg0bVFlZqSeeeKK7TxkwRIgOAAAAAAAAwK2mpkbHjh1TWlqaR3laWpoqKiq86u/YsUNTp05VWFiYuywpKUnR0dGqqqqSJD3++ONavXq1x36fffaZIiMje+AMgO5lCXQHAAAAAAAAAASP+vp6SVJcXJxHudVqVV1dnc/6iYmJXuVG9SXpN7/5jfLy8vTLX/6yG3oM9CxCdAAAAAAAAABuoaGhkiSz2XMRC5PJZFj/2rpG9S9evKh58+bpnXfe0ebNm/Xwww93Q4+BnkWIDgAAAAAAAMDNZrNJujzDPCEhwV3e0NAgq9Xqs/6V2etXu7b+2bNn9ZOf/ER33XWXDh065DXTHQhWrIkOAAAAAAAAwG3gwIEaPXq0tm7d6lG+bds2paSkeNWfNm2a3n//fbW3t7vLDh8+rMbGRk2aNEmSdOnSJc2cOVNjx47Vli1bCNDRqxCiAwAAAAAAAPCQk5OjVatWqbq6WpJUVlamiooKZWZmetVNTU1VbGysli9fro6ODjU1NWnx4sWaO3euBgwYIEkqKipS37599eKLLxouCwMEK5ZzAQAAAAAAAOAhIyNDdrtdM2bMUEtLi2w2m8rLy5WQkKDa2lqNGzdORUVFSk9Pl8ViUWVlpRYtWqT4+HiZzWalp6eroKDAfbyKigodPHhQSUlJXuunl5aW6vvf/76/TxHoNEL0qxQXF6uwsFDnzp3TXXfdpaKiIv3whz8MdLcAAACAoNOVsXNdXZ2ys7P18ccf69KlS3r44YdVUFCg8PBwP/caAAB0xcKFC7Vw4UKvcpvNptraWq+ysrIyw2N98MEHcjqdamxsVGxsrM8bkV7txIkT190+ePBguVyu69YBugvLuXxl8+bNys3NVWlpqWpra5WTk6PU1FQdP3480F0DAAAAgkpXxs5tbW2aPHmybDabjh49qkOHDunAgQPKysoKQM8BAACAriNE/0peXp6WLVumYcOGSZLS09OVnJystWvXBrhnAAAAQHDpyti5pKREp06dUn5+viwWi6KiolRUVKSNGzfqzJkz/u46AAAA0GUs5yKppqZGx44dU1pamkd5WlqaioqKtHr16gD1DAAAAAguXR0779ixQ1OnTlVYWJi7LCkpSdHR0aqqqtKsWbP80m8AQADsTLtxHdxaXCapPV6yfCGZWIoF15jwh0D3wBAhuqT6+npJUlxcnEe51WpVXV2dV32HwyGHw+F+3tTUJEk6d+6cnE5nD/bUtwv2Jr+3iWDnkqnZLpdCJHHHa3g6d46ByhXnL1wIdBcQZJyS7CEhCuno4Ot68HLp3Dm/t2m32yUpqNb77OrYub6+XomJiV7lRvWl4Bpvn28579f20Es4JdklhYjvd8PDufZzge5CcGlpD3QPEGScLpPs7ZcUZmmXmRAd1wri8TYhuqTQ0FBJ8rqhgcnkO3zMz89XXl6eV/ndd9/d/Z0DAABAcJg/P2BNNzc36/bbbw9Y+1fr6tg5NDTU543DjOpLjLcBAABuTXcErOUbjbcJ0XX57sHS5VkyCQkJ7vKGhgZZrVav+rm5ucrOznY/dzqd+vLLLxUdHX3dDwOAv9jtdsXHx+uLL75QZGRkoLsDAL0G108EG5fLpebmZq9Z34HU1bGzzWZzz16/mlF9ifE2gh+/LwDg5nD9RLDp7HibEF3SwIEDNXr0aG3dulVLlixxl2/btk0pKSle9cPDwxUeHu5RFhUV1dPdBLosMjKSX0oAcBO4fiKYBMsM9Cu6OnaeNm2aFixYoPb2dlkslz9+HD58WI2NjZo0aZLPNhhvo7fg9wUA3ByunwgmnRlvs3rbV3JycrRq1SpVV1dLksrKylRRUaHMzMwA9wwAAAAILl0ZO6empio2NlbLly9XR0eHmpqatHjxYs2dO1cDBgzwd9cBAACALmMm+lcyMjJkt9s1Y8YMtbS0yGazqby83OMrqgAAAACuP3aura3VuHHjVFRUpPT0dFksFlVWVmrRokWKj4+X2WxWenq6CgoKAn0aAAAAQKeYXDe69SiAXsfhcCg/P1+5ubleX4UGABjj+gkA6Ax+XwDAzeH6id6KEB0AAAAAAAAAAAOsiQ4AAAAAAAAAgAFCdAAAvuEuXboU6C4AAAAA31iMt4FvPkJ0AAC+wcrLyzVq1Cg5HI5O7+NyueR0OtXe3q62tjY5nc5O7ffFF19o+fLlN9vVLvnXf/1XzZkzxy9tfV0Wi0UnTpzo8n6tra2qra296XYLCws1e/bsm96/q9rb2/W///u/fmsPAAAgGDDeDjzG2/AHQnSgF5kzZ45MJpNKS0t9bp8yZYpMJpN27twpSR4/X3keExMjm80mq9WqH/zgB3rrrbf80HMAgVBaWqo5c+aora1NI0aM0D333COTyaR9+/Z51X355ZcVEhIik8kks9mskJAQhYaGKjw8XN/73vfU3t5+w/bWr1+vgwcP9sSpfC1Op1Pnzp277qOlpSXQ3ZQk7d+/X6+99pokae/evRo3blyn9vuf//kfRUVF9WDPPDmdTv31r3/Vrl279OqrryojI0MDBw7Ugw8+qObmZr/1AwC6G+NtAF3BePsyxtvdj/F28LEEugMAumbIkCHasGGD0tPTPcpramr0pz/9SXfdddd19y8tLdWECRPkcrn0zjvvKCMjQ9HR0frxj3/ck90G4Ef19fV67rnnVFJSok8++UTDhw+XJC1YsEBms1ljx4712uef/umf9Oijj8pisXg8XnrpJVVVVcliuf6Q4ezZs3rttdfU0tKiPn36uMsjIyN14MABxcfHd+9JdsHx48d17733XrfO/fffr927d/upR8Y+/fRTvfXWW3ryyScD3RVDb7/9th599FHdeeeduvvuu/WXv/xFkyZN0ieffKIhQ4YEunsA8LUx3gZwI4y3PTHe7l6Mt4MTM9GBXmbmzJnatWuXTp486VG+adMmPfTQQwoLC+vUcUwmk/7+7/9ekydP1pYtW3qiqwACoK6uTqmpqRo/frxeeeUVPfHEEzpy5IgeeeQRmUwm96yLa/Xt21cxMTG644479K1vfUu33Xab2tra9PLLLys7O/uG7T799NN64IEH1Nra6n58/PHHcjqduvPOO7v7NCVJHR0dXarvcrl8PjZt2tSt/bLZbOrXr5/70dHRoSFDhshkMrkfeXl5Pve9dOlSp6/jLpdLO3fu1M6dO7V//361t7e7n+/cuVPHjh1TY2Oj+/nnn3/eLef305/+VOfPn9eJEyf04YcfauzYsUpKSmJAD+Abg/E2gOthvG2M8Tbj7W8yZqIDvUxUVJSmT5+ujRs36sUXX5Qk9y+lN954Q1u3bu3S8c6fP6/bbrutJ7oKIACsVqv7K54XLlzQ3r17NXbsWBUUFOjIkSP67//+b/3d3/1dp45VVFSkkSNHKi0t7br11q1bp02bNnl99f3ll1/WP/7jPyo0NPTmTuYG3njjDV28eFEvvPCCRo4c2SNt3IwjR47I5XJJkqqrqzVp0iT93//9n3v7nDlzFBIS4nPfCxcuqF+/fp1qx+VyaeXKlZIuX8sdDof7uSSdPHlSzc3N7rIf//jH7llSX4fJZDLsPwB8EzDeBnA9jLcDj/E2AoGZ6EAv9LOf/UybNm1y33xk+/btMpvN+sEPftDpY5w/f15r1qzRnj179NBDD/VUVwH4WU1NjX7/+99r7ty5Gj58uCIjI/XnP/9Z+/fv15o1azR79mwlJCQoISFBJSUlhsf56KOP9NJLL+mXv/zlddt7/fXX9eKLL2rdunVatGiRe+bN0aNH9dZbb+mpp57yqH/mzBmPGSI3ely9zuy1HnzwQQ0dOlTjxo3TI488orq6us6/UDdQWFjY6T4OHjzYY9+IiAj3rJjDhw/rvvvu89jucDgMP+icPXtW0dHRhv2qq6vTz3/+c0mS2WzW9u3btX37dm3YsEERERHu59u3b9fjjz+u6dOnu58///zz7uP069ev0+dXXFws6fL/uyvvnasf27dv17/8y794fTX5448/volXHgCCA+NtAEYYb3cPxtuMt3sbZqIDvdC0adPkdDpVWVnpniXzs5/9TCaT6Yb7zp49W+Hh4QoNDdWIESO0bds2jRo1yg+9BtDTXC6XVqxYIbPZrOnTp2vt2rU6fvy4Zs+erQkTJsjpdMpkMungwYOaOXOm4dqs9fX1evjhhyVJFRUV+s53vuOz3sGDB7Vy5Uq99957GjVqlKZNm6bp06fr888/18mTJzVv3jwNGjTIY5/+/fvriy++6PQ5xcTEGG6LiIjQypUrtWDBAs2bN09Dhw7VSy+9pMzMzE4f38gTTzyh2bNnd6ru9davLCsrU3JyskdZc3OzIiIifNavrq72uYamJLW2tmrWrFn69re/3al+nT59WgMGDDBs50owdCP9+/eXJN1xxx3avn271/ZZs2Zp8eLFeuyxxzp1PADoDRhvA/CF8Tbj7asx3r61EKIDvVBISIjmzJmj3/zmN0pOTlZ5ebkKCws7te/vfvc7TZgwoWc7CCAgrp7F8OGHH2rhwoXas2ePIiMj3Tcfqq+v12OPPabi4mKfA+Y//elPSktL0z/8wz/o0Ucf1YwZM7R371796le/8voq+ne/+1395S9/cZcPGTJEu3bt0v3336+LFy9q8+bNXsc3m82y2Wzdet533323tm3bpjVr1igqKqpbjnllZsvXUV1drXfffVdHjx71KP/yyy91xx13eNW/cOGC3n//fS1dutRr28WLF90ftF5//XWv7SaTyevDxeeff66MjAyffYuLi+vsabiFhIR4zQJyuVw6ceJEpz9oAEBvwXgbgC+MtxlvX43x9q2FEB3opebNm6dRo0appKREycnJ3f5LEkDvVllZqYkTJ2rjxo3q6OjQI488otTUVB07dkwvvPCCJk2a5FHf6XRq06ZNWrp0qbKystxrwH788cdKSUnR5MmT9Yc//MFrMHrtQP+jjz7SX//6V7333nu6/fbbe/Ykr2IymXwOhq925swZn+UtLS3d3h+73a6HH35YmZmZslqtHtu++OILrzJJys/PV3x8vNdSASdOnHDPaqyoqPC5ru6IESM8zq+hoUF//OMftXHjxm46I9/ee+89uVwuJSUl9Wg7ABAIjLcBXA/jbW+Mt7sf4+3gQYgO9FL33HOPxowZo2eeecbnv5ICuLXl5+e7f/7yyy81cuRIbdiwQb/+9a81depUj7offvihFi9erIaGBv3617/WT3/6U/e2O++8U9u3b9fEiRM1YcIE7d+/3+fd7F0ul9auXauf//znevPNNzVu3LieO7mbdL2vqt5///3d1s7Ro0f12GOPKTo6Wi+99JIkaf369XrggQd04cIF1dXVKTEx0WOf3/72tyosLFRVVZXHUgFnz57VyJEj9dhjj2nNmjWdumlUe3u75s+fr/T0dN19993ddl7Xqqur0z//8z8rKytL4eHhPdYOAAQK420A18N42xvj7e7FeDu4cGNRoBebP3++JN3wTt4Abi21tbXavn27/uM//kMzZszQ8OHDdfHiRR06dMhrQC9JAwcOVEpKio4cOeIxoL8iOjpaVVVVeuGFF3wO6Hfu3Knk5GStXbtWO3bs0PTp03vkvG5WeHi4RowYIZfL5fPxzjvv6J577umWtpqbm5WcnKxRo0apvLzc/ZXPnTt36jvf+Y5GjhypWbNmeayd2NTUpH//93/X7373O49ZMYmJiUpNTdVbb72ldevWdWpA39DQoBkzZujMmTN69dVXu+WcrnXhwgWtW7dO3/3udzV+/Hg988wzPdIOAAQDxtsAfGG87YnxdvdivB2cTC6XyxXoTgAAgO7z29/+VuvXr9eYMWP0wAMPaNKkST4H493liSeekM1mU3Z2tvr27dtj7VztxIkTam1t1dChQ/3SXld8+eWX7psDXa21tVVnz571+dVSl8vVqZvVXU9ra6vGjBmj5ORkFRYW+vwaaneora3V/PnzNX/+fD300EM90gYAAEAwY7wdWIy3EQiE6AAAAN8Q7e3tXjc8AgAAANA9GG/fugjRAQAAAAAAAAAwwJroAAAAAAAAAAAYIEQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAAAAAAAADAwP8DD5WanEcfMvgAAAAASUVORK5CYII=", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 詳細比較テーブル ===\n", "          手法    目的関数値 計算時間(秒)    相対性能  時間効率\n", "         MIP 52230.00  4.1944      基準    基準\n", "多スタートローカルサーチ 72295.91  0.0211 +38.42% 0.01倍\n", "\n", "比較テーブルを保存しました: result/method_comparison_summary.csv\n"]}], "source": ["# 比較プロットの作成\n", "if mip_objective is not None and multistart_objective is not None:\n", "    # データの準備\n", "    methods = ['MIP', '多スタートローカルサーチ']\n", "    objectives = [mip_objective, multistart_objective]\n", "    times = [mip_time, multistart_time]\n", "    \n", "    # プロットの作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値の比較\n", "    bars1 = ax1.bar(methods, objectives, color=['skyblue', 'lightcoral'], alpha=0.7)\n", "    ax1.set_title('目的関数値の比較', fontsize=14, fontweight='bold')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars1, objectives):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.0f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 計算時間の比較\n", "    bars2 = ax2.bar(methods, times, color=['lightgreen', 'orange'], alpha=0.7)\n", "    ax2.set_title('計算時間の比較', fontsize=14, fontweight='bold')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars2, times):\n", "        height = bar.get_height()\n", "        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # プロットを保存\n", "    output_path = 'result/optimization_comparison.png'\n", "    plt.savefig(output_path, dpi=300, bbox_inches='tight')\n", "    print(f\"\\n比較プロットを保存しました: {output_path}\")\n", "    \n", "    plt.show()\n", "    \n", "    # 詳細比較テーブルの作成\n", "    comparison_data = {\n", "        '手法': ['MIP', '多スタートローカルサーチ'],\n", "        '目的関数値': [f'{mip_objective:.2f}', f'{multistart_objective:.2f}'],\n", "        '計算時間(秒)': [f'{mip_time:.4f}', f'{multistart_time:.4f}'],\n", "        '相対性能': ['基準', f'{objective_improvement:+.2f}%'],\n", "        '時間効率': ['基準', f'{time_ratio:.2f}倍']\n", "    }\n", "    \n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    print(f\"\\n=== 詳細比較テーブル ===\")\n", "    print(comparison_df.to_string(index=False))\n", "    \n", "    # 比較テーブルをCSVとして保存\n", "    comparison_csv_path = 'result/method_comparison_summary.csv'\n", "    comparison_df.to_csv(comparison_csv_path, index=False, encoding='utf-8')\n", "    print(f\"\\n比較テーブルを保存しました: {comparison_csv_path}\")\n", "    \n", "else:\n", "    print(\"データが不足しているため、プロットを作成できません\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}